"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9ab35eda2975\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjlhYjM1ZWRhMjk3NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useRoutePrefetch */ \"(app-pages-browser)/./src/hooks/useRoutePrefetch.ts\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/usePredictiveNavigation */ \"(app-pages-browser)/./src/hooks/usePredictiveNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst navItems = [\n    {\n        href: \"/dashboard\",\n        label: \"Dashboard\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        description: \"Overview & analytics\"\n    },\n    {\n        href: \"/my-models\",\n        label: \"My Models\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        description: \"API key management\"\n    },\n    {\n        href: \"/playground\",\n        label: \"Playground\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        description: \"Test your models\"\n    },\n    {\n        href: \"/routing-setup\",\n        label: \"Routing Setup\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        description: \"Configure routing\"\n    },\n    {\n        href: \"/logs\",\n        label: \"Logs\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        description: \"Request history\"\n    },\n    {\n        href: \"/training\",\n        label: \"Prompt Engineering\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        description: \"Custom prompts\"\n    },\n    {\n        href: \"/analytics\",\n        label: \"Analytics\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n        description: \"Advanced insights\"\n    }\n];\nfunction Sidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { isCollapsed, isHovered, isHoverDisabled, setHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__.useSidebar)();\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__.useNavigationSafe)();\n    const { navigateOptimistically } = navigationContext || {\n        navigateOptimistically: ()=>{}\n    };\n    const { prefetchOnHover } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useRoutePrefetch)();\n    const { prefetchWhenIdle } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useIntelligentPrefetch)();\n    const { prefetchChatHistory } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch)();\n    const { predictions, isLearning } = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.usePredictiveNavigation)();\n    const contextualSuggestions = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.useContextualSuggestions)();\n    // Enhanced prefetching with predictive navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const allRoutes = navItems.map({\n                \"Sidebar.useEffect.allRoutes\": (item)=>item.href\n            }[\"Sidebar.useEffect.allRoutes\"]);\n            // Combine predictive routes with standard prefetching\n            const predictiveRoutes = predictions.slice(0, 2); // Top 2 predictions\n            const contextualRoutes = contextualSuggestions.filter({\n                \"Sidebar.useEffect.contextualRoutes\": (s)=>s.priority === 'high'\n            }[\"Sidebar.useEffect.contextualRoutes\"]).map({\n                \"Sidebar.useEffect.contextualRoutes\": (s)=>s.route\n            }[\"Sidebar.useEffect.contextualRoutes\"]).slice(0, 2);\n            const routesToPrefetch = [\n                ...predictiveRoutes,\n                ...contextualRoutes,\n                ...allRoutes.filter({\n                    \"Sidebar.useEffect.routesToPrefetch\": (route)=>route !== pathname && !predictiveRoutes.includes(route) && !contextualRoutes.includes(route)\n                }[\"Sidebar.useEffect.routesToPrefetch\"]),\n                '/playground',\n                '/logs'\n            ].slice(0, 6); // Increased limit for better coverage\n            console.log(\"\\uD83E\\uDDE0 [PREDICTIVE] Prefetching routes:\", {\n                predictive: predictiveRoutes,\n                contextual: contextualRoutes,\n                total: routesToPrefetch,\n                isLearning\n            });\n            const cleanup = prefetchWhenIdle(routesToPrefetch);\n            return cleanup;\n        }\n    }[\"Sidebar.useEffect\"], [\n        pathname,\n        prefetchWhenIdle,\n        predictions,\n        contextualSuggestions,\n        isLearning\n    ]);\n    // Determine if sidebar should be expanded (hover or not collapsed)\n    const isExpanded = !isCollapsed || isHovered;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-[#030614] border-r border-gray-700/60 \".concat(isExpanded ? 'w-64' : 'w-16'),\n        onMouseEnter: ()=>!isHoverDisabled && setHovered(true),\n        onMouseLeave: ()=>!isHoverDisabled && setHovered(false),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 transition-all duration-200 ease-out \".concat(isExpanded ? 'px-6' : 'px-3'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 pt-4 transition-all duration-200 ease-out \".concat(isExpanded ? '' : 'text-center'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-0 scale-75 -translate-y-2' : 'opacity-100 scale-100 translate-y-0', \" \").concat(isExpanded ? 'absolute' : 'relative', \" w-8 h-8 bg-white rounded-lg flex items-center justify-center mx-auto p-0.5\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/roukey_logo.png\",\n                                        alt: \"RouKey\",\n                                        width: 28,\n                                        height: 28,\n                                        className: \"object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-75 translate-y-2', \" \").concat(isExpanded ? 'relative' : 'absolute top-0 left-0 w-full'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-white tracking-tight whitespace-nowrap\",\n                                            children: \"RouKey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-1 whitespace-nowrap\",\n                                            children: \"Smart LLM Router\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        children: navItems.map((item)=>{\n                            const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n                            const Icon = isActive ? item.iconSolid : item.icon;\n                            const isPredicted = predictions.includes(item.href);\n                            const contextualSuggestion = contextualSuggestions.find((s)=>s.route === item.href);\n                            // Enhanced prefetch for playground to include chat history\n                            const handlePlaygroundHover = ()=>{\n                                if (item.href === '/playground') {\n                                    // Prefetch route\n                                    prefetchOnHover(item.href, 50).onMouseEnter();\n                                    // Also prefetch chat history for current config if available\n                                    const currentConfigId = new URLSearchParams(window.location.search).get('config');\n                                    if (currentConfigId) {\n                                        prefetchChatHistory(currentConfigId);\n                                    }\n                                }\n                            };\n                            const hoverProps = item.href === '/playground' ? {\n                                onMouseEnter: handlePlaygroundHover\n                            } : prefetchOnHover(item.href, 50);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    navigateOptimistically(item.href);\n                                },\n                                className: \"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left \".concat(isActive ? 'active' : '', \" \").concat(isExpanded ? '' : 'collapsed'),\n                                title: isExpanded ? undefined : item.label,\n                                ...hoverProps,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex items-center w-full overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex items-center justify-center transition-all duration-200 ease-out \".concat(isExpanded ? 'w-5 h-5 mr-3' : 'w-10 h-10 rounded-xl', \" \").concat(!isExpanded && isActive ? 'bg-white shadow-sm' : !isExpanded ? 'bg-transparent hover:bg-white/10' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'h-5 w-5' : 'h-5 w-5', \" \").concat(isActive ? 'text-orange-500' : 'text-white')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isPredicted && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out \".concat(isExpanded ? '-top-1 -right-1 w-2 h-2' : '-top-1 -right-1 w-3 h-3'),\n                                                    title: \"Predicted next destination\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-100 translate-x-0 max-w-full' : 'opacity-0 translate-x-4 max-w-0'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between whitespace-nowrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        contextualSuggestion && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs px-1.5 py-0.5 rounded-full ml-2 \".concat(contextualSuggestion.priority === 'high' ? 'bg-blue-500/20 text-blue-300' : 'bg-gray-500/20 text-gray-300'),\n                                                            children: contextualSuggestion.priority === 'high' ? '!' : '·'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs transition-colors duration-200 whitespace-nowrap \".concat(isActive ? 'text-orange-400' : 'text-gray-400'),\n                                                    children: contextualSuggestion ? contextualSuggestion.reason : item.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"4fmIjYR/bVR+uFRRDpXMUCW8BlQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__.useSidebar,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__.useNavigationSafe,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useRoutePrefetch,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useIntelligentPrefetch,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.usePredictiveNavigation,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.useContextualSuggestions\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});