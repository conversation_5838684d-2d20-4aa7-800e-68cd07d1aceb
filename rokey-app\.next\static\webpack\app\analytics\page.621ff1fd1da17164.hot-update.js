"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ServerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Utility functions\nconst formatNumber = (num)=>{\n    if (num >= 1000000) return \"\".concat((num / 1000000).toFixed(1), \"M\");\n    if (num >= 1000) return \"\".concat((num / 1000).toFixed(1), \"K\");\n    return num.toString();\n};\nconst formatCurrency = (amount)=>{\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD',\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 4\n    }).format(amount);\n};\nconst calculateTrend = (current, previous)=>{\n    if (previous === 0) return {\n        percentage: 0,\n        isPositive: current > 0\n    };\n    const percentage = (current - previous) / previous * 100;\n    return {\n        percentage: Math.abs(percentage),\n        isPositive: percentage >= 0\n    };\n};\n// MetricCard Component\nconst MetricCard = (param)=>{\n    let { title, value, trend, icon, subtitle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400 mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: typeof value === 'number' ? formatNumber(value) : value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 11\n                                }, undefined),\n                                trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-md flex items-center space-x-1 \".concat(trend.isPositive ? 'text-green-400 bg-green-400/10' : 'text-red-400 bg-red-400/10'),\n                                    children: [\n                                        trend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                trend.percentage.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 9\n                        }, undefined),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 22\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 95,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 94,\n        columnNumber: 3\n    }, undefined);\n};\n_c = MetricCard;\nfunction AnalyticsPageContent() {\n    var _subscription_user, _subscription_user1, _subscription_user2;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { subscription } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previousPeriodData, setPreviousPeriodData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeSeriesData, setTimeSeriesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filters\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('30');\n    const [selectedConfig, setSelectedConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchCustomConfigs();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            var _subscription_user, _subscription_user1;\n            console.log('[Analytics] Subscription state:', {\n                user: subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id,\n                loading: subscription === null || subscription === void 0 ? void 0 : subscription.loading,\n                error: subscription === null || subscription === void 0 ? void 0 : subscription.error\n            });\n            if (subscription === null || subscription === void 0 ? void 0 : (_subscription_user1 = subscription.user) === null || _subscription_user1 === void 0 ? void 0 : _subscription_user1.id) {\n                fetchAnalyticsData();\n            } else if (!(subscription === null || subscription === void 0 ? void 0 : subscription.loading)) {\n                // If not loading and no user, show error\n                setError('Authentication required. Please log in to view analytics.');\n                setLoading(false);\n            }\n        }\n    }[\"AnalyticsPageContent.useEffect\"], [\n        subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id,\n        timeRange,\n        selectedConfig,\n        fetchAnalyticsData\n    ]);\n    const fetchCustomConfigs = async ()=>{\n        try {\n            const response = await fetch('/api/custom-configs');\n            if (response.ok) {\n                const configs = await response.json();\n                setCustomConfigs(configs);\n            }\n        } catch (err) {\n            console.error('Error fetching configs:', err);\n        }\n    };\n    const fetchAnalyticsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchAnalyticsData]\": async ()=>{\n            try {\n                var _subscription_user, _data_grouped_data, _data_summary, _data_summary1;\n                setLoading(true);\n                setError(null);\n                console.log('[Analytics] Starting data fetch for user:', subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id);\n                // First, try a simple test to see if the API is accessible\n                try {\n                    const testResponse = await fetch('/api/analytics/summary?groupBy=provider');\n                    console.log('[Analytics] Test API response status:', testResponse.status);\n                    if (testResponse.status === 401) {\n                        throw new Error('Authentication required. Please log in to view analytics.');\n                    }\n                } catch (testErr) {\n                    console.error('[Analytics] Test API failed:', testErr);\n                }\n                // Build query parameters\n                const params = new URLSearchParams();\n                const startDate = new Date();\n                startDate.setDate(startDate.getDate() - parseInt(timeRange));\n                params.append('startDate', startDate.toISOString());\n                if (selectedConfig) {\n                    params.append('customApiConfigId', selectedConfig);\n                }\n                console.log('[Analytics] Fetching with params:', params.toString());\n                // Fetch just the main analytics data first\n                const response = await fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=provider\"));\n                console.log('[Analytics] Response status:', response.status);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    console.error('[Analytics] API Error:', errorText);\n                    if (response.status === 401) {\n                        throw new Error('Authentication required. Please log in to view analytics.');\n                    }\n                    throw new Error(\"Failed to fetch analytics data: \".concat(response.status, \" \").concat(errorText));\n                }\n                const data = await response.json();\n                console.log('[Analytics] Received data:', {\n                    summary: data.summary,\n                    groupedDataCount: ((_data_grouped_data = data.grouped_data) === null || _data_grouped_data === void 0 ? void 0 : _data_grouped_data.length) || 0,\n                    hasData: ((_data_summary = data.summary) === null || _data_summary === void 0 ? void 0 : _data_summary.total_requests) > 0\n                });\n                setAnalyticsData(data);\n                // Only fetch additional data if we have some basic data\n                if (((_data_summary1 = data.summary) === null || _data_summary1 === void 0 ? void 0 : _data_summary1.total_requests) > 0) {\n                    try {\n                        // Build previous period params for comparison\n                        const prevParams = new URLSearchParams();\n                        const prevStartDate = new Date();\n                        prevStartDate.setDate(prevStartDate.getDate() - parseInt(timeRange) * 2);\n                        const prevEndDate = new Date();\n                        prevEndDate.setDate(prevEndDate.getDate() - parseInt(timeRange));\n                        prevParams.append('startDate', prevStartDate.toISOString());\n                        prevParams.append('endDate', prevEndDate.toISOString());\n                        if (selectedConfig) {\n                            prevParams.append('customApiConfigId', selectedConfig);\n                        }\n                        // Fetch additional data\n                        const [prevResponse, timeSeriesResponse] = await Promise.all([\n                            fetch(\"/api/analytics/summary?\".concat(prevParams.toString(), \"&groupBy=provider\")),\n                            fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=day\"))\n                        ]);\n                        const prevData = prevResponse.ok ? await prevResponse.json() : null;\n                        const timeSeriesData = timeSeriesResponse.ok ? await timeSeriesResponse.json() : null;\n                        setPreviousPeriodData(prevData);\n                        if (timeSeriesData === null || timeSeriesData === void 0 ? void 0 : timeSeriesData.grouped_data) {\n                            const formattedTimeSeries = timeSeriesData.grouped_data.map({\n                                \"AnalyticsPageContent.useCallback[fetchAnalyticsData].formattedTimeSeries\": (item)=>({\n                                        date: item.period || item.name,\n                                        cost: item.cost || 0,\n                                        requests: item.requests || 0,\n                                        tokens: (item.input_tokens || 0) + (item.output_tokens || 0),\n                                        latency: item.avg_latency || 0\n                                    })\n                            }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData].formattedTimeSeries\"]);\n                            setTimeSeriesData(formattedTimeSeries);\n                            console.log('[Analytics] Formatted time series:', formattedTimeSeries.length, 'items');\n                        }\n                    } catch (additionalErr) {\n                        console.warn('[Analytics] Failed to fetch additional data:', additionalErr);\n                    // Don't fail the whole request if additional data fails\n                    }\n                }\n            } catch (err) {\n                console.error('[Analytics] Error fetching data:', err);\n                setError(err.message);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData]\"], [\n        subscription === null || subscription === void 0 ? void 0 : (_subscription_user1 = subscription.user) === null || _subscription_user1 === void 0 ? void 0 : _subscription_user1.id,\n        timeRange,\n        selectedConfig\n    ]);\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-800 rounded w-1/3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(4)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-lg p-6 border border-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-800 rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-800 rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-800 rounded w-1/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 285,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 284,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Analytics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/20 border border-red-800 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-400 mb-4\",\n                                    children: [\n                                        \"Error loading analytics: \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchAnalyticsData,\n                                    className: \"px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 transition-colors\",\n                                    children: \"Retry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 307,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 306,\n            columnNumber: 7\n        }, this);\n    }\n    const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n    const previousSummary = previousPeriodData === null || previousPeriodData === void 0 ? void 0 : previousPeriodData.summary;\n    // Calculate trends\n    const costTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0, previousSummary.total_cost) : null;\n    const requestTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0, previousSummary.total_requests) : null;\n    const latencyTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0, previousSummary.average_latency || 0) : null;\n    const successTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0, previousSummary.success_rate) : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#040716] text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-semibold text-white\",\n                                        children: \"Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-3 py-1 text-sm text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Workspace\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-3 py-1 text-sm bg-cyan-500 text-white rounded\",\n                                                children: \"Organisation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search Filter\",\n                                                className: \"bg-transparent border-none outline-none text-gray-400 placeholder-gray-500 w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: timeRange,\n                                        onChange: (e)=>setTimeRange(e.target.value),\n                                        className: \"px-3 py-1 bg-gray-800 border border-gray-700 rounded text-sm text-white focus:outline-none focus:border-cyan-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"7\",\n                                                children: \"Last 7 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"30\",\n                                                children: \"Last 30 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"90\",\n                                                children: \"Last 90 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('overview'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'overview' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('users'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'users' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Users\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('errors'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'errors' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Errors\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('cache'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'cache' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Cache\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('feedback'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'feedback' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Feedback\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('metadata'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'metadata' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Metadata\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: [\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Total Request Made\",\n                                        value: (summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0,\n                                        trend: requestTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Average Latency\",\n                                        value: \"\".concat(Math.round((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0), \"ms\"),\n                                        trend: latencyTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"User Feedback\",\n                                        value: \"\".concat(((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0).toFixed(1), \"%\"),\n                                        trend: successTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Total Cost\",\n                                        value: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0),\n                                        trend: costTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, this),\n                            (!summary || summary.total_requests === 0) && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto mb-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-2\",\n                                        children: \"No Analytics Data Yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"Start making API requests to see your analytics data here.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mb-6\",\n                                        children: \"Analytics will appear once you begin using your API configurations.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 17\n                                    }, this),\n                                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/50 rounded-lg p-4 text-left max-w-md mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-white mb-2\",\n                                                children: \"Debug Info:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"User ID: \",\n                                                            (subscription === null || subscription === void 0 ? void 0 : (_subscription_user2 = subscription.user) === null || _subscription_user2 === void 0 ? void 0 : _subscription_user2.id) || 'Not logged in'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Time Range: \",\n                                                            timeRange,\n                                                            \" days\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Selected Config: \",\n                                                            selectedConfig || 'All configs'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Loading: \",\n                                                            loading ? 'Yes' : 'No'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Error: \",\n                                                            error || 'None'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Cost\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold text-white\",\n                                                                        children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    costTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm px-2 py-1 rounded flex items-center space-x-1 \".concat(costTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'),\n                                                                        children: [\n                                                                            costTrend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 531,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 533,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    costTrend.percentage.toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 535,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 527,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-48 relative bg-gray-800/50 rounded\",\n                                                children: timeSeriesData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 400 120\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"costGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#10b981\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 552,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#10b981\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            [\n                                                                ...Array(5)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                    x1: \"0\",\n                                                                    y1: i * 24,\n                                                                    x2: \"400\",\n                                                                    y2: i * 24,\n                                                                    stroke: \"#374151\",\n                                                                    strokeWidth: \"0.5\",\n                                                                    opacity: \"0.3\"\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                fill: \"none\",\n                                                                stroke: \"#10b981\",\n                                                                strokeWidth: \"2\",\n                                                                points: timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxCost = Math.max(...timeSeriesData.map((item)=>item.cost));\n                                                                    const y = 120 - d.cost / maxCost * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' ')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                fill: \"url(#costGradient)\",\n                                                                points: \"0,120 \".concat(timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxCost = Math.max(...timeSeriesData.map((item)=>item.cost));\n                                                                    const y = 120 - d.cost / maxCost * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' '), \" 400,120\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-8 w-8 mx-auto mb-2 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"No data available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Latency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold text-white\",\n                                                                        children: [\n                                                                            Math.round((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0),\n                                                                            \"ms\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 613,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    latencyTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm px-2 py-1 rounded flex items-center space-x-1 \".concat(latencyTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'),\n                                                                        children: [\n                                                                            latencyTrend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 621,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 623,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    latencyTrend.percentage.toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 625,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 617,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-48 relative bg-gray-800/50 rounded\",\n                                                children: timeSeriesData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 400 120\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"latencyGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#f59e0b\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 642,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#f59e0b\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 643,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            [\n                                                                ...Array(5)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                    x1: \"0\",\n                                                                    y1: i * 24,\n                                                                    x2: \"400\",\n                                                                    y2: i * 24,\n                                                                    stroke: \"#374151\",\n                                                                    strokeWidth: \"0.5\",\n                                                                    opacity: \"0.3\"\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                fill: \"none\",\n                                                                stroke: \"#f59e0b\",\n                                                                strokeWidth: \"2\",\n                                                                points: timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxLatency = Math.max(...timeSeriesData.map((item)=>item.latency || 0));\n                                                                    const y = 120 - (d.latency || 0) / Math.max(maxLatency, 1) * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' ')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                fill: \"url(#latencyGradient)\",\n                                                                points: \"0,120 \".concat(timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxLatency = Math.max(...timeSeriesData.map((item)=>item.latency || 0));\n                                                                    const y = 120 - (d.latency || 0) / Math.max(maxLatency, 1) * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' '), \" 400,120\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-8 w-8 mx-auto mb-2 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"No data available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Tokens Used\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 704,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400 mt-1\",\n                                                                children: \"March 28\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 708,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_tokens) || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 716,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"8.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 711,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-32 relative bg-gray-800/50 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 mb-4 text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-yellow-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 727,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Input Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 728,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 726,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 731,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Output Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 732,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 735,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Total Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 736,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 725,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-16\",\n                                                            children: [\n                                                                ...Array(20)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute w-1 h-1 rounded-full \".concat(i % 3 === 0 ? 'bg-yellow-500' : i % 3 === 1 ? 'bg-green-500' : 'bg-blue-500'),\n                                                                    style: {\n                                                                        left: \"\".concat(Math.random() * 90, \"%\"),\n                                                                        top: \"\".concat(Math.random() * 80, \"%\")\n                                                                    }\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 743,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 767,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 771,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"3.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 770,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data.slice(0, 5).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 text-sm text-gray-400 truncate\",\n                                                                children: provider.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 mx-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 bg-gray-800 rounded-full overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full rounded-full \".concat(index === 0 ? 'bg-pink-500' : index === 1 ? 'bg-purple-500' : index === 2 ? 'bg-cyan-500' : index === 3 ? 'bg-green-500' : 'bg-yellow-500'),\n                                                                        style: {\n                                                                            width: \"\".concat(provider.requests / ((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 1) * 100, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 789,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 787,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 w-12 text-right\",\n                                                                children: formatNumber(provider.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, provider.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Unique Users\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 817,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: (summary === null || summary === void 0 ? void 0 : summary.successful_requests) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 825,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"3.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 820,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-32 relative bg-gray-800/50 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 200 80\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"waveGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#8b5cf6\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 836,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#8b5cf6\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 837,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 835,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0,40 Q50,20 100,40 T200,40\",\n                                                                fill: \"none\",\n                                                                stroke: \"#8b5cf6\",\n                                                                strokeWidth: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0,40 Q50,20 100,40 T200,40 L200,80 L0,80 Z\",\n                                                                fill: \"url(#waveGradient)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 833,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 832,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 831,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'users' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 865,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Users Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 866,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"User analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 867,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 864,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'errors' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 873,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Error Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 874,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Error analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 875,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 872,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'cache' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 881,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Cache Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 882,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Cache analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 883,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 880,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'feedback' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 889,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Feedback Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 890,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Feedback analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 891,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 888,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'metadata' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 897,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Metadata Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 898,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Metadata analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 899,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 896,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 335,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPageContent, \"V6GaieX1/66vfxScG50cnS5Gmxs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription\n    ];\n});\n_c1 = AnalyticsPageContent;\nfunction AnalyticsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-800 rounded w-1/3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 913,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(4)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-lg p-6 border border-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-800 rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-800 rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 918,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-800 rounded w-1/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 919,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 916,\n                                    columnNumber: 17\n                                }, void 0))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 914,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 912,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 911,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 910,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 927,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 909,\n        columnNumber: 5\n    }, this);\n}\n_c2 = AnalyticsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MetricCard\");\n$RefreshReg$(_c1, \"AnalyticsPageContent\");\n$RefreshReg$(_c2, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});