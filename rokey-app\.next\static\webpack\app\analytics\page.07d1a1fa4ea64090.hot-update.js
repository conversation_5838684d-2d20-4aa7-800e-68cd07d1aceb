"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ServerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Utility functions\nconst formatNumber = (num)=>{\n    if (num >= 1000000) return \"\".concat((num / 1000000).toFixed(1), \"M\");\n    if (num >= 1000) return \"\".concat((num / 1000).toFixed(1), \"K\");\n    return num.toString();\n};\nconst formatCurrency = (amount)=>{\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD',\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 4\n    }).format(amount);\n};\nconst calculateTrend = (current, previous)=>{\n    if (previous === 0) return {\n        percentage: 0,\n        isPositive: current > 0\n    };\n    const percentage = (current - previous) / previous * 100;\n    return {\n        percentage: Math.abs(percentage),\n        isPositive: percentage >= 0\n    };\n};\n// MetricCard Component\nconst MetricCard = (param)=>{\n    let { title, value, trend, icon, subtitle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400 mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: typeof value === 'number' ? formatNumber(value) : value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 11\n                                }, undefined),\n                                trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-md flex items-center space-x-1 \".concat(trend.isPositive ? 'text-green-400 bg-green-400/10' : 'text-red-400 bg-red-400/10'),\n                                    children: [\n                                        trend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                trend.percentage.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 9\n                        }, undefined),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 22\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 95,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 94,\n        columnNumber: 3\n    }, undefined);\n};\n_c = MetricCard;\nfunction AnalyticsPageContent() {\n    var _subscription_user, _subscription_user1;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { subscription } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previousPeriodData, setPreviousPeriodData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeSeriesData, setTimeSeriesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filters\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('30');\n    const [selectedConfig, setSelectedConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchCustomConfigs();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            var _subscription_user, _subscription_user1;\n            console.log('[Analytics] Subscription state:', {\n                user: subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id,\n                loading: subscription === null || subscription === void 0 ? void 0 : subscription.loading,\n                error: subscription === null || subscription === void 0 ? void 0 : subscription.error,\n                isAuthenticated: subscription === null || subscription === void 0 ? void 0 : subscription.isAuthenticated\n            });\n            // Wait for subscription hook to finish loading before making decisions\n            if (subscription === null || subscription === void 0 ? void 0 : subscription.loading) {\n                console.log('[Analytics] Subscription still loading, waiting...');\n                return;\n            }\n            if (subscription === null || subscription === void 0 ? void 0 : (_subscription_user1 = subscription.user) === null || _subscription_user1 === void 0 ? void 0 : _subscription_user1.id) {\n                console.log('[Analytics] User authenticated, fetching data...');\n                fetchAnalyticsData();\n            } else {\n                // If subscription finished loading and no user, show error\n                console.log('[Analytics] No user found after loading completed');\n                setError('Authentication required. Please log in to view analytics.');\n                setLoading(false);\n            }\n        }\n    }[\"AnalyticsPageContent.useEffect\"], [\n        subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id,\n        subscription === null || subscription === void 0 ? void 0 : subscription.loading,\n        timeRange,\n        selectedConfig\n    ]);\n    const fetchCustomConfigs = async ()=>{\n        try {\n            const response = await fetch('/api/custom-configs');\n            if (response.ok) {\n                const configs = await response.json();\n                setCustomConfigs(configs);\n            }\n        } catch (err) {\n            console.error('Error fetching configs:', err);\n        }\n    };\n    const fetchAnalyticsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchAnalyticsData]\": async ()=>{\n            try {\n                var _subscription_user, _data_grouped_data, _data_summary, _data_summary1;\n                setLoading(true);\n                setError(null);\n                console.log('[Analytics] Starting data fetch for user:', subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id);\n                // First, try a simple test to see if the API is accessible\n                try {\n                    const testResponse = await fetch('/api/analytics/summary?groupBy=provider');\n                    console.log('[Analytics] Test API response status:', testResponse.status);\n                    if (testResponse.status === 401) {\n                        throw new Error('Authentication required. Please log in to view analytics.');\n                    }\n                } catch (testErr) {\n                    console.error('[Analytics] Test API failed:', testErr);\n                }\n                // Build query parameters\n                const params = new URLSearchParams();\n                const startDate = new Date();\n                startDate.setDate(startDate.getDate() - parseInt(timeRange));\n                params.append('startDate', startDate.toISOString());\n                if (selectedConfig) {\n                    params.append('customApiConfigId', selectedConfig);\n                }\n                console.log('[Analytics] Fetching with params:', params.toString());\n                // Fetch just the main analytics data first\n                const response = await fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=provider\"));\n                console.log('[Analytics] Response status:', response.status);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    console.error('[Analytics] API Error:', errorText);\n                    if (response.status === 401) {\n                        throw new Error('Authentication required. Please log in to view analytics.');\n                    }\n                    throw new Error(\"Failed to fetch analytics data: \".concat(response.status, \" \").concat(errorText));\n                }\n                const data = await response.json();\n                console.log('[Analytics] Received data:', {\n                    summary: data.summary,\n                    groupedDataCount: ((_data_grouped_data = data.grouped_data) === null || _data_grouped_data === void 0 ? void 0 : _data_grouped_data.length) || 0,\n                    hasData: ((_data_summary = data.summary) === null || _data_summary === void 0 ? void 0 : _data_summary.total_requests) > 0\n                });\n                setAnalyticsData(data);\n                // Only fetch additional data if we have some basic data\n                if (((_data_summary1 = data.summary) === null || _data_summary1 === void 0 ? void 0 : _data_summary1.total_requests) > 0) {\n                    try {\n                        // Build previous period params for comparison\n                        const prevParams = new URLSearchParams();\n                        const prevStartDate = new Date();\n                        prevStartDate.setDate(prevStartDate.getDate() - parseInt(timeRange) * 2);\n                        const prevEndDate = new Date();\n                        prevEndDate.setDate(prevEndDate.getDate() - parseInt(timeRange));\n                        prevParams.append('startDate', prevStartDate.toISOString());\n                        prevParams.append('endDate', prevEndDate.toISOString());\n                        if (selectedConfig) {\n                            prevParams.append('customApiConfigId', selectedConfig);\n                        }\n                        // Fetch additional data\n                        const [prevResponse, timeSeriesResponse] = await Promise.all([\n                            fetch(\"/api/analytics/summary?\".concat(prevParams.toString(), \"&groupBy=provider\")),\n                            fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=day\"))\n                        ]);\n                        const prevData = prevResponse.ok ? await prevResponse.json() : null;\n                        const timeSeriesData = timeSeriesResponse.ok ? await timeSeriesResponse.json() : null;\n                        setPreviousPeriodData(prevData);\n                        if (timeSeriesData === null || timeSeriesData === void 0 ? void 0 : timeSeriesData.grouped_data) {\n                            const formattedTimeSeries = timeSeriesData.grouped_data.map({\n                                \"AnalyticsPageContent.useCallback[fetchAnalyticsData].formattedTimeSeries\": (item)=>({\n                                        date: item.period || item.name,\n                                        cost: item.cost || 0,\n                                        requests: item.requests || 0,\n                                        tokens: (item.input_tokens || 0) + (item.output_tokens || 0),\n                                        latency: item.avg_latency || 0\n                                    })\n                            }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData].formattedTimeSeries\"]);\n                            setTimeSeriesData(formattedTimeSeries);\n                            console.log('[Analytics] Formatted time series:', formattedTimeSeries.length, 'items');\n                        }\n                    } catch (additionalErr) {\n                        console.warn('[Analytics] Failed to fetch additional data:', additionalErr);\n                    // Don't fail the whole request if additional data fails\n                    }\n                }\n            } catch (err) {\n                console.error('[Analytics] Error fetching data:', err);\n                setError(err.message);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData]\"], [\n        timeRange,\n        selectedConfig\n    ]);\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-800 rounded w-1/3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(4)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-lg p-6 border border-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-800 rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-800 rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-800 rounded w-1/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 293,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen w-full bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Analytics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/20 border border-red-800 rounded-lg p-6 max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-400 mb-4\",\n                                    children: [\n                                        \"Error loading analytics: \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchAnalyticsData,\n                                    className: \"px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 transition-colors\",\n                                    children: \"Retry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 315,\n            columnNumber: 7\n        }, this);\n    }\n    const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n    const previousSummary = previousPeriodData === null || previousPeriodData === void 0 ? void 0 : previousPeriodData.summary;\n    // Calculate trends\n    const costTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0, previousSummary.total_cost) : null;\n    const requestTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0, previousSummary.total_requests) : null;\n    const latencyTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0, previousSummary.average_latency || 0) : null;\n    const successTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0, previousSummary.success_rate) : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-semibold text-white\",\n                                        children: \"Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-3 py-1 text-sm text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Workspace\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-3 py-1 text-sm bg-cyan-500 text-white rounded\",\n                                                children: \"Organisation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search Filter\",\n                                                className: \"bg-transparent border-none outline-none text-gray-400 placeholder-gray-500 w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: timeRange,\n                                        onChange: (e)=>setTimeRange(e.target.value),\n                                        className: \"px-3 py-1 bg-gray-800 border border-gray-700 rounded text-sm text-white focus:outline-none focus:border-cyan-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"7\",\n                                                children: \"Last 7 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"30\",\n                                                children: \"Last 30 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"90\",\n                                                children: \"Last 90 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('overview'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'overview' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('users'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'users' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Users\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('errors'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'errors' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Errors\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('cache'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'cache' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Cache\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('feedback'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'feedback' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Feedback\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('metadata'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'metadata' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Metadata\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 384,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Total Request Made\",\n                                        value: (summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0,\n                                        trend: requestTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Average Latency\",\n                                        value: \"\".concat(Math.round((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0), \"ms\"),\n                                        trend: latencyTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"User Feedback\",\n                                        value: \"\".concat(((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0).toFixed(1), \"%\"),\n                                        trend: successTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Total Cost\",\n                                        value: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0),\n                                        trend: costTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this),\n                            (!summary || summary.total_requests === 0) && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto mb-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-2\",\n                                        children: \"No Analytics Data Yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"Start making API requests to see your analytics data here.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mb-6\",\n                                        children: \"Analytics will appear once you begin using your API configurations.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 17\n                                    }, this),\n                                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/50 rounded-lg p-4 text-left max-w-md mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-white mb-2\",\n                                                children: \"Debug Info:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"User ID: \",\n                                                            (subscription === null || subscription === void 0 ? void 0 : (_subscription_user1 = subscription.user) === null || _subscription_user1 === void 0 ? void 0 : _subscription_user1.id) || 'Not logged in'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Time Range: \",\n                                                            timeRange,\n                                                            \" days\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Selected Config: \",\n                                                            selectedConfig || 'All configs'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Loading: \",\n                                                            loading ? 'Yes' : 'No'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Error: \",\n                                                            error || 'None'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    console.log('[Analytics] Manual refresh triggered');\n                                                    fetchAnalyticsData();\n                                                },\n                                                className: \"mt-3 px-3 py-1 bg-cyan-500 text-white text-xs rounded hover:bg-cyan-600 transition-colors\",\n                                                children: \"Refresh Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Cost\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold text-white\",\n                                                                        children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 541,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    costTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm px-2 py-1 rounded flex items-center space-x-1 \".concat(costTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'),\n                                                                        children: [\n                                                                            costTrend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 549,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 551,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    costTrend.percentage.toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 553,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-48 relative bg-gray-800/50 rounded\",\n                                                children: timeSeriesData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 400 120\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"costGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#10b981\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 570,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#10b981\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 571,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            [\n                                                                ...Array(5)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                    x1: \"0\",\n                                                                    y1: i * 24,\n                                                                    x2: \"400\",\n                                                                    y2: i * 24,\n                                                                    stroke: \"#374151\",\n                                                                    strokeWidth: \"0.5\",\n                                                                    opacity: \"0.3\"\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                fill: \"none\",\n                                                                stroke: \"#10b981\",\n                                                                strokeWidth: \"2\",\n                                                                points: timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxCost = Math.max(...timeSeriesData.map((item)=>item.cost));\n                                                                    const y = 120 - d.cost / maxCost * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' ')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                fill: \"url(#costGradient)\",\n                                                                points: \"0,120 \".concat(timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxCost = Math.max(...timeSeriesData.map((item)=>item.cost));\n                                                                    const y = 120 - d.cost / maxCost * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' '), \" 400,120\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-8 w-8 mx-auto mb-2 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"No data available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Latency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold text-white\",\n                                                                        children: [\n                                                                            Math.round((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0),\n                                                                            \"ms\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    latencyTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm px-2 py-1 rounded flex items-center space-x-1 \".concat(latencyTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'),\n                                                                        children: [\n                                                                            latencyTrend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 639,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 641,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    latencyTrend.percentage.toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 643,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-48 relative bg-gray-800/50 rounded\",\n                                                children: timeSeriesData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 400 120\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"latencyGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#f59e0b\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 660,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#f59e0b\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            [\n                                                                ...Array(5)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                    x1: \"0\",\n                                                                    y1: i * 24,\n                                                                    x2: \"400\",\n                                                                    y2: i * 24,\n                                                                    stroke: \"#374151\",\n                                                                    strokeWidth: \"0.5\",\n                                                                    opacity: \"0.3\"\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                fill: \"none\",\n                                                                stroke: \"#f59e0b\",\n                                                                strokeWidth: \"2\",\n                                                                points: timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxLatency = Math.max(...timeSeriesData.map((item)=>item.latency || 0));\n                                                                    const y = 120 - (d.latency || 0) / Math.max(maxLatency, 1) * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' ')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                fill: \"url(#latencyGradient)\",\n                                                                points: \"0,120 \".concat(timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxLatency = Math.max(...timeSeriesData.map((item)=>item.latency || 0));\n                                                                    const y = 120 - (d.latency || 0) / Math.max(maxLatency, 1) * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' '), \" 400,120\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-8 w-8 mx-auto mb-2 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"No data available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Tokens Used\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 722,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400 mt-1\",\n                                                                children: \"March 28\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 726,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_tokens) || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"8.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-32 relative bg-gray-800/50 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 mb-4 text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-yellow-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 745,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Input Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 749,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Output Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 750,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 753,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Total Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 754,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 752,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-16\",\n                                                            children: [\n                                                                ...Array(20)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute w-1 h-1 rounded-full \".concat(i % 3 === 0 ? 'bg-yellow-500' : i % 3 === 1 ? 'bg-green-500' : 'bg-blue-500'),\n                                                                    style: {\n                                                                        left: \"\".concat(Math.random() * 90, \"%\"),\n                                                                        top: \"\".concat(Math.random() * 80, \"%\")\n                                                                    }\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 782,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 785,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"3.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data.slice(0, 5).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 text-sm text-gray-400 truncate\",\n                                                                children: provider.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 mx-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 bg-gray-800 rounded-full overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full rounded-full \".concat(index === 0 ? 'bg-pink-500' : index === 1 ? 'bg-purple-500' : index === 2 ? 'bg-cyan-500' : index === 3 ? 'bg-green-500' : 'bg-yellow-500'),\n                                                                        style: {\n                                                                            width: \"\".concat(provider.requests / ((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 1) * 100, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 807,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 805,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 w-12 text-right\",\n                                                                children: formatNumber(provider.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 820,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, provider.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Unique Users\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 835,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 834,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: (summary === null || summary === void 0 ? void 0 : summary.successful_requests) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 843,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"3.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 844,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 842,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 838,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-32 relative bg-gray-800/50 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 200 80\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"waveGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#8b5cf6\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 854,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#8b5cf6\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 855,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 852,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0,40 Q50,20 100,40 T200,40\",\n                                                                fill: \"none\",\n                                                                stroke: \"#8b5cf6\",\n                                                                strokeWidth: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0,40 Q50,20 100,40 T200,40 L200,80 L0,80 Z\",\n                                                                fill: \"url(#waveGradient)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 717,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'users' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 883,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Users Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"User analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 885,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 882,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'errors' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 891,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Error Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 892,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Error analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 893,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 890,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'cache' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 899,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Cache Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 900,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Cache analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 901,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 898,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'feedback' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 907,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Feedback Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 908,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Feedback analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 909,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 906,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'metadata' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Metadata Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 916,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Metadata analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 914,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 458,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 344,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPageContent, \"V6GaieX1/66vfxScG50cnS5Gmxs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription\n    ];\n});\n_c1 = AnalyticsPageContent;\nfunction AnalyticsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-800 rounded w-1/3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 931,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(4)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-lg p-6 border border-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-800 rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 935,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-800 rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 936,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-800 rounded w-1/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 937,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 934,\n                                    columnNumber: 17\n                                }, void 0))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 930,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 929,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 928,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 945,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 927,\n        columnNumber: 5\n    }, this);\n}\n_c2 = AnalyticsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MetricCard\");\n$RefreshReg$(_c1, \"AnalyticsPageContent\");\n$RefreshReg$(_c2, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});