'use client';

import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { useEffect } from 'react';
import {
  HomeIcon,
  KeyIcon,
  MapIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  ChartBarIcon,
  BeakerIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  KeyIcon as KeyIconSolid,
  MapIcon as MapIconSolid,
  DocumentTextIcon as DocumentTextIconSolid,
  AcademicCapIcon as AcademicCapIconSolid,
  ChartBarIcon as ChartBarIconSolid,
  BeakerIcon as BeakerIconSolid
} from '@heroicons/react/24/solid';
import { useSidebar } from '@/contexts/SidebarContext';
import { useNavigationSafe } from '@/contexts/NavigationContext';
import { useRoutePrefetch, useIntelligentPrefetch } from '@/hooks/useRoutePrefetch';
import { useChatHistoryPrefetch } from '@/hooks/useChatHistory';
import { usePredictiveNavigation, useContextualSuggestions } from '@/hooks/usePredictiveNavigation';

const navItems = [
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: HomeIcon,
    iconSolid: HomeIconSolid,
    description: "Overview & analytics"
  },
  {
    href: "/my-models",
    label: "My Models",
    icon: KeyIcon,
    iconSolid: KeyIconSolid,
    description: "API key management"
  },
  {
    href: "/playground",
    label: "Playground",
    icon: BeakerIcon,
    iconSolid: BeakerIconSolid,
    description: "Test your models"
  },
  {
    href: "/routing-setup",
    label: "Routing Setup",
    icon: MapIcon,
    iconSolid: MapIconSolid,
    description: "Configure routing"
  },
  {
    href: "/logs",
    label: "Logs",
    icon: DocumentTextIcon,
    iconSolid: DocumentTextIconSolid,
    description: "Request history"
  },
  {
    href: "/training",
    label: "Prompt Engineering",
    icon: AcademicCapIcon,
    iconSolid: AcademicCapIconSolid,
    description: "Custom prompts"
  },
  {
    href: "/analytics",
    label: "Analytics",
    icon: ChartBarIcon,
    iconSolid: ChartBarIconSolid,
    description: "Advanced insights"
  },
];

export default function Sidebar() {
  const pathname = usePathname();
  const { isCollapsed, isHovered, isHoverDisabled, setHovered } = useSidebar();
  const navigationContext = useNavigationSafe();
  const { navigateOptimistically } = navigationContext || { navigateOptimistically: () => {} };
  const { prefetchOnHover } = useRoutePrefetch();
  const { prefetchWhenIdle } = useIntelligentPrefetch();
  const { prefetchChatHistory } = useChatHistoryPrefetch();
  const { predictions, isLearning } = usePredictiveNavigation();
  const contextualSuggestions = useContextualSuggestions();

  // Enhanced prefetching with predictive navigation
  useEffect(() => {
    const allRoutes = navItems.map(item => item.href);

    // Combine predictive routes with standard prefetching
    const predictiveRoutes = predictions.slice(0, 2); // Top 2 predictions
    const contextualRoutes = contextualSuggestions
      .filter(s => s.priority === 'high')
      .map(s => s.route)
      .slice(0, 2);

    const routesToPrefetch = [
      ...predictiveRoutes, // Highest priority: user patterns
      ...contextualRoutes, // High priority: contextual suggestions
      ...allRoutes.filter(route =>
        route !== pathname &&
        !predictiveRoutes.includes(route) &&
        !contextualRoutes.includes(route)
      ), // Other routes
      '/playground', // Fallback: commonly visited
      '/logs', // Fallback: commonly visited
    ].slice(0, 6); // Increased limit for better coverage

    console.log(`🧠 [PREDICTIVE] Prefetching routes:`, {
      predictive: predictiveRoutes,
      contextual: contextualRoutes,
      total: routesToPrefetch,
      isLearning
    });

    const cleanup = prefetchWhenIdle(routesToPrefetch);
    return cleanup;
  }, [pathname, prefetchWhenIdle, predictions, contextualSuggestions, isLearning]);

  // Determine if sidebar should be expanded (hover or not collapsed)
  const isExpanded = !isCollapsed || isHovered;

  return (
    <aside
      className={`sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-[#030614] border-r-2 border-gray-600/80 ${
        isExpanded ? 'w-64' : 'w-16'
      }`}
      style={{
        boxShadow: '2px 0 8px rgba(0, 0, 0, 0.3)'
      }}
      onMouseEnter={() => !isHoverDisabled && setHovered(true)}
      onMouseLeave={() => !isHoverDisabled && setHovered(false)}
    >
      <div className="flex-1 relative overflow-hidden">

        <div className={`p-6 transition-all duration-200 ease-out ${isExpanded ? 'px-6' : 'px-3'}`}>
          {/* Logo/Brand Section */}
          <div className={`mb-8 pt-4 transition-all duration-200 ease-out ${isExpanded ? '' : 'text-center'}`}>
            <div className="relative overflow-hidden">
              {/* Collapsed Logo */}
              <div className={`transition-all duration-200 ease-out ${
                isExpanded ? 'opacity-0 scale-75 -translate-y-2' : 'opacity-100 scale-100 translate-y-0'
              } ${isExpanded ? 'absolute' : 'relative'} w-8 h-8 bg-white rounded-lg flex items-center justify-center mx-auto p-0.5`}>
                <Image
                  src="/roukey_logo.png"
                  alt="RouKey"
                  width={28}
                  height={28}
                  className="object-cover"
                />
              </div>

              {/* Expanded Logo */}
              <div className={`transition-all duration-200 ease-out ${
                isExpanded ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-75 translate-y-2'
              } ${isExpanded ? 'relative' : 'absolute top-0 left-0 w-full'}`}>
                <h1 className="text-2xl font-bold text-white tracking-tight whitespace-nowrap">RouKey</h1>
                <p className="text-sm text-gray-400 mt-1 whitespace-nowrap">Smart LLM Router</p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="space-y-2">
            {navItems.map((item) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
              const Icon = isActive ? item.iconSolid : item.icon;
              const isPredicted = predictions.includes(item.href);
              const contextualSuggestion = contextualSuggestions.find(s => s.route === item.href);

              // Enhanced prefetch for playground to include chat history
              const handlePlaygroundHover = () => {
                if (item.href === '/playground') {
                  // Prefetch route
                  prefetchOnHover(item.href, 50).onMouseEnter();

                  // Also prefetch chat history for current config if available
                  const currentConfigId = new URLSearchParams(window.location.search).get('config');
                  if (currentConfigId) {
                    prefetchChatHistory(currentConfigId);
                  }
                }
              };

              const hoverProps = item.href === '/playground'
                ? { onMouseEnter: handlePlaygroundHover }
                : prefetchOnHover(item.href, 50);

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={(e) => {
                    e.preventDefault();
                    navigateOptimistically(item.href);
                  }}
                  className={`sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left ${
                    isActive ? 'active' : ''
                  } ${isExpanded ? '' : 'collapsed'}`}
                  title={isExpanded ? undefined : item.label}
                  {...hoverProps}
                >
                  <div className="relative flex items-center w-full overflow-hidden">
                    {/* Icon - always visible */}
                    <div className={`relative flex items-center justify-center transition-all duration-200 ease-out ${
                      isExpanded ? 'w-5 h-5 mr-3' : 'w-10 h-10 rounded-xl'
                    } ${
                      !isExpanded && isActive
                        ? 'bg-white shadow-sm'
                        : !isExpanded
                        ? 'bg-transparent hover:bg-white/10'
                        : ''
                    }`}>
                      <Icon className={`transition-all duration-200 ease-out ${
                        isExpanded ? 'h-5 w-5' : 'h-5 w-5'
                      } ${isActive ? 'text-orange-500' : 'text-white'}`} />
                      {isPredicted && !isActive && (
                        <div className={`absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out ${
                          isExpanded ? '-top-1 -right-1 w-2 h-2' : '-top-1 -right-1 w-3 h-3'
                        }`} title="Predicted next destination" />
                      )}
                    </div>

                    {/* Text content - slides in/out */}
                    <div className={`flex-1 transition-all duration-200 ease-out ${
                      isExpanded
                        ? 'opacity-100 translate-x-0 max-w-full'
                        : 'opacity-0 translate-x-4 max-w-0'
                    }`}>
                      <div className="flex items-center justify-between whitespace-nowrap">
                        <div className="font-medium text-sm">{item.label}</div>
                        {contextualSuggestion && !isActive && (
                          <span className={`text-xs px-1.5 py-0.5 rounded-full ml-2 ${
                            contextualSuggestion.priority === 'high'
                              ? 'bg-blue-500/20 text-blue-300'
                              : 'bg-gray-500/20 text-gray-300'
                          }`}>
                            {contextualSuggestion.priority === 'high' ? '!' : '·'}
                          </span>
                        )}
                      </div>
                      <div className={`text-xs transition-colors duration-200 whitespace-nowrap ${
                        isActive ? 'text-orange-400' : 'text-gray-400'
                      }`}>
                        {contextualSuggestion ? contextualSuggestion.reason : item.description}
                      </div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </nav>
        </div>
      </div>
    </aside>
  );
}

