'use client';

import { useState, useEffect, use<PERSON><PERSON>back, ChangeEvent, FormEvent } from 'react';
import {
  AdjustmentsHorizontalIcon, CalendarDaysIcon, DocumentMagnifyingGlassIcon,
  EyeIcon, ArrowPathIcon, ArrowUpIcon, ArrowDownIcon, ArrowsUpDownIcon,
  FunnelIcon, ClockIcon, DocumentTextIcon
} from '@heroicons/react/24/outline';
import LogDetailModal from '@/components/logs/LogDetailModal';
import Button from '@/components/ui/Button';
import Card, { CardHeader, CardContent } from '@/components/ui/Card';
import Input, { Select } from '@/components/ui/Input';
import { LoadingTable } from '@/components/ui/LoadingSpinner';
import { transformRoleUsed, getRoleUsedBadgeClass, formatProviderName, formatModelName } from '@/utils/logFormatting';
import { useSubscription } from '@/hooks/useSubscription';

// Types from MyModelsPage - assuming they might be moved to a shared types file later
interface CustomApiConfigMini {
  id: string;
  name: string;
}

// Define types for Log entry and Pagination
interface RequestLog {
  id: string;
  request_timestamp: string;
  custom_api_config_id: string | null;
  custom_api_config_name?: string; // Will be populated client-side after fetching configs
  role_requested: string | null;
  role_used: string | null;
  llm_provider_name: string | null;
  llm_model_name: string | null;
  status_code: number | null;
  llm_provider_latency_ms: number | null;
  processing_duration_ms: number | null;
  input_tokens: number | null;
  output_tokens: number | null;
  cost: number | null;
  is_multimodal: boolean | null;
  ip_address: string | null;
  user_id: string | null;
  error_message: string | null;
  error_source: string | null;
  error_details_zod: string | null;
  llm_provider_status_code: number | null;
  request_payload_summary: any | null;
  response_payload_summary: any | null;
}

interface PaginationState {
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

interface FiltersState {
  startDate: string;
  endDate: string;
  customApiConfigId: string;
  status: string;
}

interface SortState {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// Define which columns are sortable and map display name to field name
const sortableColumns: { label: string; field: string; defaultSortOrder?: 'asc' | 'desc' }[] = [
  { label: 'Timestamp', field: 'request_timestamp', defaultSortOrder: 'desc' },
  { label: 'API Model', field: 'custom_api_config_id' }, // Sorting by name would require join or client-side sort on resolved names
  { label: 'Role Used', field: 'role_used' },
  { label: 'Provider', field: 'llm_provider_name' },
  { label: 'LLM Model', field: 'llm_model_name' },
  { label: 'Status', field: 'status_code' },
  { label: 'Latency (LLM)', field: 'llm_provider_latency_ms' },
  { label: 'Latency (RoKey)', field: 'processing_duration_ms' },
  { label: 'Input Tokens', field: 'input_tokens' },
  { label: 'Output Tokens', field: 'output_tokens' },
];

const DEFAULT_PAGE_SIZE = 10; // Reduced for faster initial load

export default function LogsPage() {
  const { user } = useSubscription();
  const [logs, setLogs] = useState<RequestLog[]>([]);
  const [pagination, setPagination] = useState<PaginationState | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingConfigs, setIsLoadingConfigs] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [apiConfigs, setApiConfigs] = useState<CustomApiConfigMini[]>([]);

  const initialFilters: FiltersState = {
    startDate: '',
    endDate: '',
    customApiConfigId: 'all',
    status: 'all',
  };
  const [filters, setFilters] = useState<FiltersState>(initialFilters);
  const [showFilters, setShowFilters] = useState<boolean>(false);

  const [customConfigNameMap, setCustomConfigNameMap] = useState<Record<string, string>>({});

  // State for Log Detail Modal
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedLog, setSelectedLog] = useState<RequestLog | null>(null);

  const [sort, setSort] = useState<SortState>({ sortBy: 'request_timestamp', sortOrder: 'desc' });

  const fetchApiConfigs = async () => {
    setIsLoadingConfigs(true);
    try {
      const response = await fetch('/api/custom-configs');
      if (!response.ok) {
        throw new Error('Failed to fetch API model configurations');
      }
      const data: CustomApiConfigMini[] = await response.json();
      setApiConfigs(data);
      const nameMap: Record<string, string> = {};
      data.forEach(config => { nameMap[config.id] = config.name; });
      setCustomConfigNameMap(nameMap);
    } catch (err: any) {
      setError(`Error fetching configurations: ${err.message}`);
    } finally {
      setIsLoadingConfigs(false);
    }
  };

  const fetchLogs = useCallback(async (page = 1, currentFilters: FiltersState, currentSort: SortState) => {
    setIsLoading(true);
    setError(null);
    try {
      const params: Record<string, string> = {
        page: page.toString(),
        pageSize: DEFAULT_PAGE_SIZE.toString(),
        sortBy: currentSort.sortBy,
        sortOrder: currentSort.sortOrder,
      };
      if (currentFilters.startDate) params.startDate = new Date(currentFilters.startDate).toISOString();
      if (currentFilters.endDate) params.endDate = new Date(currentFilters.endDate).toISOString();
      if (currentFilters.customApiConfigId !== 'all') params.customApiConfigId = currentFilters.customApiConfigId;
      if (currentFilters.status !== 'all') params.status = currentFilters.status;
      
      const response = await fetch(`/api/logs?${new URLSearchParams(params).toString()}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.details || 'Failed to fetch logs');
      }
      const data = await response.json();
      setLogs(data.logs || []);
      setPagination(data.pagination || null);
    } catch (err: any) {
      setError(err.message);
      setLogs([]);
      setPagination(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    // Only fetch data when user is authenticated
    if (user) {
      // Delay initial data fetching to improve perceived performance
      const timer = setTimeout(() => {
        fetchApiConfigs();
        fetchLogs(1, filters, sort);
      }, 100);

      return () => clearTimeout(timer);
    } else if (user === null) {
      // User is explicitly null (not authenticated), stop loading
      setIsLoading(false);
      setIsLoadingConfigs(false);
    }
    // If user is undefined, we're still loading auth state, keep loading
  }, [fetchLogs, filters, sort, user]); // Add user dependency

  const handleFilterChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFilters(prev => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleApplyFilters = (e?: FormEvent<HTMLFormElement>) => {
    e?.preventDefault();
    fetchLogs(1, filters, sort);
  };

  const handleResetFilters = () => {
    setFilters(initialFilters);
    const defaultSort = { sortBy: 'request_timestamp', sortOrder: 'desc' } as SortState;
    setSort(defaultSort);
    fetchLogs(1, initialFilters, defaultSort);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && (!pagination || newPage <= pagination.totalPages)) {
      fetchLogs(newPage, filters, sort);
    }
  };

  const handleSort = (field: string) => {
    const newSortOrder = (sort.sortBy === field && sort.sortOrder === 'asc') ? 'desc' : 'asc';
    const newSortState: SortState = { sortBy: field, sortOrder: newSortOrder };
    setSort(newSortState);
    fetchLogs(1, filters, newSortState);
  };

  // Handlers for Log Detail Modal
  const handleOpenModal = (log: RequestLog) => {
    setSelectedLog(log);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedLog(null);
  };

  const getStatusClass = (statusCode: number | null) => {
    if (statusCode === null) return 'bg-gray-600 text-gray-100';
    if (statusCode >= 200 && statusCode < 300) return 'bg-green-600 text-green-100';
    if (statusCode >= 400) return 'bg-red-600 text-red-100';
    return 'bg-yellow-500 text-yellow-100'; // For 3xx or other statuses
  };

  const SortableHeader: React.FC<{ column: typeof sortableColumns[0] }> = ({ column }) => {
    const isCurrentSortColumn = sort.sortBy === column.field;
    return (
      <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
        <button
          onClick={() => handleSort(column.field)}
          className="flex items-center space-x-2 hover:text-white transition-colors duration-200 group"
        >
          <span>{column.label}</span>
          {isCurrentSortColumn ? (
            sort.sortOrder === 'asc' ?
              <ArrowUpIcon className="h-4 w-4 text-orange-400" /> :
              <ArrowDownIcon className="h-4 w-4 text-orange-400" />
          ) : (
            <ArrowsUpDownIcon className="h-4 w-4 text-gray-500 group-hover:text-gray-300" />
          )}
        </button>
      </th>
    );
  };

  return (
    <div className="min-h-screen bg-[#040716] text-white">
      <div className="space-y-8 animate-fade-in p-6">
        {/* Header with Gradient Welcome */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
          <div>
            <h1 className="text-4xl font-bold mb-4">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400">
                📊 Request Logs
              </span>
            </h1>
            <p className="text-gray-300 text-lg">
              Monitor and analyze your API request history with detailed insights
            </p>
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 flex items-center space-x-2 ${
              showFilters
                ? "bg-blue-500 hover:bg-blue-600 text-white shadow-lg"
                : "bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 border border-gray-700/50"
            }`}
          >
            <FunnelIcon className="h-5 w-5" />
            <span>{showFilters ? 'Hide Filters' : 'Show Filters'}</span>
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-2xl p-6">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
              <p className="text-red-300 font-medium">{error}</p>
            </div>
          </div>
        )}

        {/* Filters */}
        {showFilters && (
          <div className="bg-gray-800/30 border border-gray-700/50 rounded-2xl p-8 animate-scale-in backdrop-blur-sm">
            <div className="mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-2xl font-semibold text-white mb-2">Filter Logs</h3>
                  <p className="text-gray-300">Narrow down your search results with precision</p>
                </div>
                <div className="flex items-center space-x-3 text-sm text-gray-400 bg-gray-700/30 px-4 py-2 rounded-xl">
                  <ClockIcon className="h-4 w-4" />
                  <span>Real-time updates</span>
                </div>
              </div>
            </div>
            <form onSubmit={handleApplyFilters} className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    <CalendarDaysIcon className="h-4 w-4 inline mr-2 text-blue-400" />
                    Start Date
                  </label>
                  <input
                    type="date"
                    name="startDate"
                    value={filters.startDate}
                    onChange={handleFilterChange}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50 transition-all duration-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    <CalendarDaysIcon className="h-4 w-4 inline mr-2 text-blue-400" />
                    End Date
                  </label>
                  <input
                    type="date"
                    name="endDate"
                    value={filters.endDate}
                    onChange={handleFilterChange}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50 transition-all duration-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    <DocumentMagnifyingGlassIcon className="h-4 w-4 inline mr-2 text-purple-400" />
                    API Model
                  </label>
                  <select
                    name="customApiConfigId"
                    value={filters.customApiConfigId}
                    onChange={handleFilterChange}
                    disabled={isLoadingConfigs}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50 transition-all duration-200"
                  >
                    <option value="all" className="bg-gray-800 text-white">All Models</option>
                    {apiConfigs.map(config => (
                      <option key={config.id} value={config.id} className="bg-gray-800 text-white">{config.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    <AdjustmentsHorizontalIcon className="h-4 w-4 inline mr-2 text-green-400" />
                    Status
                  </label>
                  <select
                    name="status"
                    value={filters.status}
                    onChange={handleFilterChange}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50 transition-all duration-200"
                  >
                    <option value="all" className="bg-gray-800 text-white">All Statuses</option>
                    <option value="success" className="bg-gray-800 text-white">Success</option>
                    <option value="error" className="bg-gray-800 text-white">Error</option>
                  </select>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <button
                  type="submit"
                  className="flex-1 px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl"
                >
                  <DocumentMagnifyingGlassIcon className="h-5 w-5" />
                  <span>Apply Filters</span>
                </button>
                <button
                  type="button"
                  onClick={handleResetFilters}
                  className="flex-1 px-6 py-3 bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 rounded-xl font-medium transition-all duration-200 flex items-center justify-center space-x-2 border border-gray-600/50"
                >
                  <ArrowPathIcon className="h-5 w-5" />
                  <span>Reset Filters</span>
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Loading State */}
        {isLoading && <LoadingTable rows={8} columns={11} />}

        {/* Empty State */}
        {!isLoading && !logs.length && !error && (
          <div className="bg-gray-800/30 border border-gray-700/50 rounded-2xl text-center py-16 backdrop-blur-sm">
            <div className="max-w-md mx-auto">
              <div className="w-20 h-20 bg-gradient-to-br from-orange-500/20 to-orange-600/20 rounded-2xl flex items-center justify-center mx-auto mb-8 border border-orange-500/30">
                <DocumentTextIcon className="h-10 w-10 text-orange-400" />
              </div>
              <h3 className="text-2xl font-semibold text-white mb-4">No Logs Found</h3>
              <p className="text-gray-300 text-lg leading-relaxed">
                No request logs match your criteria. Once you make requests to the unified API, they will appear here with detailed insights.
              </p>
            </div>
          </div>
        )}

        {/* Logs Table */}
        {!isLoading && logs.length > 0 && (
          <>
            <div className="bg-gray-800/30 border border-gray-700/50 rounded-2xl overflow-hidden backdrop-blur-sm">
              <div className="overflow-x-auto">
                <table className="min-w-full text-sm">
                  <thead className="bg-gray-700/50 border-b border-gray-600/50">
                    <tr>
                      {sortableColumns.map(col => <SortableHeader key={col.field} column={col} />)}
                      <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        Multimodal
                      </th>
                      <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700/50 bg-transparent">
                    {logs.map((log, index) => (
                      <tr
                        key={log.id}
                        className="hover:bg-gray-700/30 transition-colors duration-200 animate-slide-in"
                        style={{ animationDelay: `${index * 50}ms` }}
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-white">
                          <div className="flex items-center space-x-2">
                            <ClockIcon className="h-4 w-4 text-gray-400" />
                            <span>{new Date(log.request_timestamp).toLocaleString()}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-white">
                          <div className="font-medium">
                            {log.custom_api_config_id ? (customConfigNameMap[log.custom_api_config_id] || log.custom_api_config_id.substring(0,8) + '...') : 'N/A'}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-white">
                        {(() => {
                          const roleInfo = transformRoleUsed(log.role_used);
                          return (
                            <span className={getRoleUsedBadgeClass(roleInfo.type)}>
                              {roleInfo.text}
                            </span>
                          );
                        })()}
                      </td>
                        <td className="px-6 py-4 text-white">
                          <span className="font-medium">{formatProviderName(log.llm_provider_name)}</span>
                        </td>
                        <td className="px-6 py-4 text-white truncate max-w-xs" title={formatModelName(log.llm_model_name)}>
                          <span className="font-medium">{formatModelName(log.llm_model_name)}</span>
                        </td>
                        <td className="px-6 py-4">
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusClass(log.status_code)}`}>
                            {log.status_code || 'N/A'}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-right text-white">
                          {log.llm_provider_latency_ms !== null ? `${log.llm_provider_latency_ms} ms` : '-'}
                        </td>
                        <td className="px-6 py-4 text-right text-white">
                          {log.processing_duration_ms !== null ? `${log.processing_duration_ms} ms` : '-'}
                        </td>
                        <td className="px-6 py-4 text-right text-white">
                          {log.input_tokens !== null ? log.input_tokens.toLocaleString() : '-'}
                        </td>
                        <td className="px-6 py-4 text-right text-white">
                          {log.output_tokens !== null ? log.output_tokens.toLocaleString() : '-'}
                        </td>
                        <td className="px-6 py-4 text-center">
                          {log.is_multimodal ? (
                            <span className="w-3 h-3 bg-green-400 rounded-full inline-block"></span>
                          ) : (
                            <span className="w-3 h-3 bg-gray-600 rounded-full inline-block"></span>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          <button
                            onClick={() => handleOpenModal(log)}
                            className="p-2 text-gray-400 hover:text-orange-400 hover:bg-orange-500/20 rounded-lg transition-all duration-200"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                        </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

            {/* Pagination Controls */}
            {pagination && pagination.totalPages > 1 && (
              <div className="bg-gray-800/30 border border-gray-700/50 rounded-2xl p-6 backdrop-blur-sm">
                <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
                  <div className="text-sm text-gray-300">
                    Showing <span className="font-medium text-white">{(pagination.currentPage - 1) * pagination.pageSize + 1}</span>
                    {logs.length > 0 ? ` - ${Math.min(pagination.currentPage * pagination.pageSize, pagination.totalCount)}` : ''}
                    {' '}of <span className="font-medium text-white">{pagination.totalCount}</span> logs
                  </div>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => handlePageChange(pagination.currentPage - 1)}
                      disabled={pagination.currentPage <= 1 || isLoading}
                      className="px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 disabled:bg-gray-800/50 disabled:opacity-50 text-gray-300 rounded-xl font-medium transition-all duration-200 border border-gray-600/50"
                    >
                      Previous
                    </button>
                    <span className="px-4 py-2 text-sm text-gray-300 bg-gray-700/30 rounded-xl">
                      Page {pagination.currentPage} of {pagination.totalPages}
                    </span>
                    <button
                      onClick={() => handlePageChange(pagination.currentPage + 1)}
                      disabled={pagination.currentPage >= pagination.totalPages || isLoading}
                      className="px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 disabled:bg-gray-800/50 disabled:opacity-50 text-gray-300 rounded-xl font-medium transition-all duration-200 border border-gray-600/50"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </>
          )}

          {/* Log Detail Modal */}
          {isModalOpen && selectedLog && (
            <LogDetailModal
              log={selectedLog}
              onClose={handleCloseModal}
              apiConfigNameMap={customConfigNameMap}
            />
          )}
        </div>
      </div>
    );
} 