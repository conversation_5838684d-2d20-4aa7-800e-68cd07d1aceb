"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cf3b7e26841e\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImNmM2I3ZTI2ODQxZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useRoutePrefetch */ \"(app-pages-browser)/./src/hooks/useRoutePrefetch.ts\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/usePredictiveNavigation */ \"(app-pages-browser)/./src/hooks/usePredictiveNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst navItems = [\n    {\n        href: \"/dashboard\",\n        label: \"Dashboard\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        description: \"Overview & analytics\"\n    },\n    {\n        href: \"/my-models\",\n        label: \"My Models\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        description: \"API key management\"\n    },\n    {\n        href: \"/playground\",\n        label: \"Playground\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        description: \"Test your models\"\n    },\n    {\n        href: \"/routing-setup\",\n        label: \"Routing Setup\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        description: \"Configure routing\"\n    },\n    {\n        href: \"/logs\",\n        label: \"Logs\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        description: \"Request history\"\n    },\n    {\n        href: \"/training\",\n        label: \"Prompt Engineering\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        description: \"Custom prompts\"\n    },\n    {\n        href: \"/analytics\",\n        label: \"Analytics\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n        description: \"Advanced insights\"\n    }\n];\nfunction Sidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { isCollapsed, isHovered, isHoverDisabled, setHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__.useSidebar)();\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__.useNavigationSafe)();\n    const { navigateOptimistically } = navigationContext || {\n        navigateOptimistically: ()=>{}\n    };\n    const { prefetchOnHover } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useRoutePrefetch)();\n    const { prefetchWhenIdle } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useIntelligentPrefetch)();\n    const { prefetchChatHistory } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch)();\n    const { predictions, isLearning } = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.usePredictiveNavigation)();\n    const contextualSuggestions = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.useContextualSuggestions)();\n    // Enhanced prefetching with predictive navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const allRoutes = navItems.map({\n                \"Sidebar.useEffect.allRoutes\": (item)=>item.href\n            }[\"Sidebar.useEffect.allRoutes\"]);\n            // Combine predictive routes with standard prefetching\n            const predictiveRoutes = predictions.slice(0, 2); // Top 2 predictions\n            const contextualRoutes = contextualSuggestions.filter({\n                \"Sidebar.useEffect.contextualRoutes\": (s)=>s.priority === 'high'\n            }[\"Sidebar.useEffect.contextualRoutes\"]).map({\n                \"Sidebar.useEffect.contextualRoutes\": (s)=>s.route\n            }[\"Sidebar.useEffect.contextualRoutes\"]).slice(0, 2);\n            const routesToPrefetch = [\n                ...predictiveRoutes,\n                ...contextualRoutes,\n                ...allRoutes.filter({\n                    \"Sidebar.useEffect.routesToPrefetch\": (route)=>route !== pathname && !predictiveRoutes.includes(route) && !contextualRoutes.includes(route)\n                }[\"Sidebar.useEffect.routesToPrefetch\"]),\n                '/playground',\n                '/logs'\n            ].slice(0, 6); // Increased limit for better coverage\n            console.log(\"\\uD83E\\uDDE0 [PREDICTIVE] Prefetching routes:\", {\n                predictive: predictiveRoutes,\n                contextual: contextualRoutes,\n                total: routesToPrefetch,\n                isLearning\n            });\n            const cleanup = prefetchWhenIdle(routesToPrefetch);\n            return cleanup;\n        }\n    }[\"Sidebar.useEffect\"], [\n        pathname,\n        prefetchWhenIdle,\n        predictions,\n        contextualSuggestions,\n        isLearning\n    ]);\n    // Determine if sidebar should be expanded (hover or not collapsed)\n    const isExpanded = !isCollapsed || isHovered;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-[#030614] border-r-2 border-gray-600/80 \".concat(isExpanded ? 'w-64' : 'w-16'),\n        style: {\n            boxShadow: '2px 0 8px rgba(0, 0, 0, 0.3)'\n        },\n        onMouseEnter: ()=>!isHoverDisabled && setHovered(true),\n        onMouseLeave: ()=>!isHoverDisabled && setHovered(false),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 transition-all duration-200 ease-out \".concat(isExpanded ? 'px-6' : 'px-3'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 pt-4 transition-all duration-200 ease-out \".concat(isExpanded ? '' : 'text-center'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-0 scale-75 -translate-y-2' : 'opacity-100 scale-100 translate-y-0', \" \").concat(isExpanded ? 'absolute' : 'relative', \" w-8 h-8 bg-white rounded-lg flex items-center justify-center mx-auto p-0.5\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/roukey_logo.png\",\n                                        alt: \"RouKey\",\n                                        width: 28,\n                                        height: 28,\n                                        className: \"object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-75 translate-y-2', \" \").concat(isExpanded ? 'relative' : 'absolute top-0 left-0 w-full'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-white tracking-tight whitespace-nowrap\",\n                                            children: \"RouKey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-1 whitespace-nowrap\",\n                                            children: \"Smart LLM Router\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        children: navItems.map((item)=>{\n                            const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n                            const Icon = isActive ? item.iconSolid : item.icon;\n                            const isPredicted = predictions.includes(item.href);\n                            const contextualSuggestion = contextualSuggestions.find((s)=>s.route === item.href);\n                            // Enhanced prefetch for playground to include chat history\n                            const handlePlaygroundHover = ()=>{\n                                if (item.href === '/playground') {\n                                    // Prefetch route\n                                    prefetchOnHover(item.href, 50).onMouseEnter();\n                                    // Also prefetch chat history for current config if available\n                                    const currentConfigId = new URLSearchParams(window.location.search).get('config');\n                                    if (currentConfigId) {\n                                        prefetchChatHistory(currentConfigId);\n                                    }\n                                }\n                            };\n                            const hoverProps = item.href === '/playground' ? {\n                                onMouseEnter: handlePlaygroundHover\n                            } : prefetchOnHover(item.href, 50);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    navigateOptimistically(item.href);\n                                },\n                                className: \"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left \".concat(isActive ? 'active' : '', \" \").concat(isExpanded ? '' : 'collapsed'),\n                                title: isExpanded ? undefined : item.label,\n                                ...hoverProps,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex items-center w-full overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex items-center justify-center transition-all duration-200 ease-out \".concat(isExpanded ? 'w-5 h-5 mr-3' : 'w-10 h-10 rounded-xl', \" \").concat(!isExpanded && isActive ? 'bg-white shadow-sm' : !isExpanded ? 'bg-transparent hover:bg-white/10' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'h-5 w-5' : 'h-5 w-5', \" \").concat(isActive ? 'text-orange-500' : 'text-white')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isPredicted && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out \".concat(isExpanded ? '-top-1 -right-1 w-2 h-2' : '-top-1 -right-1 w-3 h-3'),\n                                                    title: \"Predicted next destination\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-100 translate-x-0 max-w-full' : 'opacity-0 translate-x-4 max-w-0'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between whitespace-nowrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        contextualSuggestion && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs px-1.5 py-0.5 rounded-full ml-2 \".concat(contextualSuggestion.priority === 'high' ? 'bg-blue-500/20 text-blue-300' : 'bg-gray-500/20 text-gray-300'),\n                                                            children: contextualSuggestion.priority === 'high' ? '!' : '·'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs transition-colors duration-200 whitespace-nowrap \".concat(isActive ? 'text-orange-400' : 'text-gray-400'),\n                                                    children: contextualSuggestion ? contextualSuggestion.reason : item.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"4fmIjYR/bVR+uFRRDpXMUCW8BlQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__.useSidebar,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__.useNavigationSafe,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useRoutePrefetch,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useIntelligentPrefetch,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.usePredictiveNavigation,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.useContextualSuggestions\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});