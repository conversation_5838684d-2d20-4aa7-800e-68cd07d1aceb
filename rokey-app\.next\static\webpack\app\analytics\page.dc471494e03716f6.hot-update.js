"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ServerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,MagnifyingGlassIcon,ServerIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Utility functions\nconst formatNumber = (num)=>{\n    if (num >= 1000000) return \"\".concat((num / 1000000).toFixed(1), \"M\");\n    if (num >= 1000) return \"\".concat((num / 1000).toFixed(1), \"K\");\n    return num.toString();\n};\nconst formatCurrency = (amount)=>{\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD',\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 4\n    }).format(amount);\n};\nconst calculateTrend = (current, previous)=>{\n    if (previous === 0) return {\n        percentage: 0,\n        isPositive: current > 0\n    };\n    const percentage = (current - previous) / previous * 100;\n    return {\n        percentage: Math.abs(percentage),\n        isPositive: percentage >= 0\n    };\n};\n// MetricCard Component\nconst MetricCard = (param)=>{\n    let { title, value, trend, icon, subtitle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400 mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: typeof value === 'number' ? formatNumber(value) : value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 11\n                                }, undefined),\n                                trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-md flex items-center space-x-1 \".concat(trend.isPositive ? 'text-green-400 bg-green-400/10' : 'text-red-400 bg-red-400/10'),\n                                    children: [\n                                        trend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                trend.percentage.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 9\n                        }, undefined),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 22\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 95,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 94,\n        columnNumber: 3\n    }, undefined);\n};\n_c = MetricCard;\nfunction AnalyticsPageContent() {\n    var _subscription_user, _subscription_user1;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { subscription } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previousPeriodData, setPreviousPeriodData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeSeriesData, setTimeSeriesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filters\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('30');\n    const [selectedConfig, setSelectedConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchCustomConfigs();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            var _subscription_user;\n            if (subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id) {\n                fetchAnalyticsData();\n            }\n        }\n    }[\"AnalyticsPageContent.useEffect\"], [\n        subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id,\n        timeRange,\n        selectedConfig\n    ]);\n    const fetchCustomConfigs = async ()=>{\n        try {\n            const response = await fetch('/api/custom-configs');\n            if (response.ok) {\n                const configs = await response.json();\n                setCustomConfigs(configs);\n            }\n        } catch (err) {\n            console.error('Error fetching configs:', err);\n        }\n    };\n    const fetchAnalyticsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchAnalyticsData]\": async ()=>{\n            try {\n                var _subscription_user, _data_grouped_data, _timeSeriesData_grouped_data;\n                setLoading(true);\n                setError(null);\n                console.log('[Analytics] Starting data fetch for user:', subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id);\n                // Build query parameters\n                const params = new URLSearchParams();\n                const startDate = new Date();\n                startDate.setDate(startDate.getDate() - parseInt(timeRange));\n                params.append('startDate', startDate.toISOString());\n                if (selectedConfig) {\n                    params.append('customApiConfigId', selectedConfig);\n                }\n                console.log('[Analytics] Fetching with params:', params.toString());\n                // Build previous period params for comparison\n                const prevParams = new URLSearchParams();\n                const prevStartDate = new Date();\n                prevStartDate.setDate(prevStartDate.getDate() - parseInt(timeRange) * 2);\n                const prevEndDate = new Date();\n                prevEndDate.setDate(prevEndDate.getDate() - parseInt(timeRange));\n                prevParams.append('startDate', prevStartDate.toISOString());\n                prevParams.append('endDate', prevEndDate.toISOString());\n                if (selectedConfig) {\n                    prevParams.append('customApiConfigId', selectedConfig);\n                }\n                // Fetch analytics data\n                const [response, prevResponse, timeSeriesResponse] = await Promise.all([\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=provider\")),\n                    fetch(\"/api/analytics/summary?\".concat(prevParams.toString(), \"&groupBy=provider\")),\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=day\"))\n                ]);\n                console.log('[Analytics] Response status:', response.status);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    console.error('[Analytics] API Error:', errorText);\n                    throw new Error(\"Failed to fetch analytics data: \".concat(response.status, \" \").concat(errorText));\n                }\n                const data = await response.json();\n                const prevData = prevResponse.ok ? await prevResponse.json() : null;\n                const timeSeriesData = timeSeriesResponse.ok ? await timeSeriesResponse.json() : null;\n                console.log('[Analytics] Received data:', {\n                    summary: data.summary,\n                    groupedDataCount: ((_data_grouped_data = data.grouped_data) === null || _data_grouped_data === void 0 ? void 0 : _data_grouped_data.length) || 0,\n                    timeSeriesCount: (timeSeriesData === null || timeSeriesData === void 0 ? void 0 : (_timeSeriesData_grouped_data = timeSeriesData.grouped_data) === null || _timeSeriesData_grouped_data === void 0 ? void 0 : _timeSeriesData_grouped_data.length) || 0\n                });\n                setAnalyticsData(data);\n                setPreviousPeriodData(prevData);\n                if (timeSeriesData === null || timeSeriesData === void 0 ? void 0 : timeSeriesData.grouped_data) {\n                    const formattedTimeSeries = timeSeriesData.grouped_data.map({\n                        \"AnalyticsPageContent.useCallback[fetchAnalyticsData].formattedTimeSeries\": (item)=>({\n                                date: item.period || item.name,\n                                cost: item.cost || 0,\n                                requests: item.requests || 0,\n                                tokens: (item.input_tokens || 0) + (item.output_tokens || 0),\n                                latency: item.avg_latency || 0\n                            })\n                    }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData].formattedTimeSeries\"]);\n                    setTimeSeriesData(formattedTimeSeries);\n                    console.log('[Analytics] Formatted time series:', formattedTimeSeries.length, 'items');\n                }\n            } catch (err) {\n                console.error('[Analytics] Error fetching data:', err);\n                setError(err.message);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData]\"], [\n        subscription === null || subscription === void 0 ? void 0 : (_subscription_user1 = subscription.user) === null || _subscription_user1 === void 0 ? void 0 : _subscription_user1.id,\n        timeRange,\n        selectedConfig\n    ]);\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-800 rounded w-1/3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(4)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-lg p-6 border border-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-800 rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-800 rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-800 rounded w-1/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 246,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Analytics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/20 border border-red-800 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-400 mb-4\",\n                                    children: [\n                                        \"Error loading analytics: \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchAnalyticsData,\n                                    className: \"px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 transition-colors\",\n                                    children: \"Retry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, this);\n    }\n    const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n    const previousSummary = previousPeriodData === null || previousPeriodData === void 0 ? void 0 : previousPeriodData.summary;\n    // Calculate trends\n    const costTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0, previousSummary.total_cost) : null;\n    const requestTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0, previousSummary.total_requests) : null;\n    const latencyTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0, previousSummary.average_latency || 0) : null;\n    const successTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0, previousSummary.success_rate) : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#040716] text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-semibold text-white\",\n                                        children: \"Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-3 py-1 text-sm text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Workspace\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-3 py-1 text-sm bg-cyan-500 text-white rounded\",\n                                                children: \"Organisation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search Filter\",\n                                                className: \"bg-transparent border-none outline-none text-gray-400 placeholder-gray-500 w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: timeRange,\n                                        onChange: (e)=>setTimeRange(e.target.value),\n                                        className: \"px-3 py-1 bg-gray-800 border border-gray-700 rounded text-sm text-white focus:outline-none focus:border-cyan-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"7\",\n                                                children: \"Last 7 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"30\",\n                                                children: \"Last 30 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"90\",\n                                                children: \"Last 90 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('overview'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'overview' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('users'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'users' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Users\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('errors'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'errors' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Errors\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('cache'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'cache' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Cache\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('feedback'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'feedback' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Feedback\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('metadata'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'metadata' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Metadata\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: [\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Total Request Made\",\n                                        value: (summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0,\n                                        trend: requestTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Average Latency\",\n                                        value: \"\".concat(Math.round((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0), \"ms\"),\n                                        trend: latencyTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"User Feedback\",\n                                        value: \"\".concat(((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0).toFixed(1), \"%\"),\n                                        trend: successTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Total Cost\",\n                                        value: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0),\n                                        trend: costTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this),\n                            (!summary || summary.total_requests === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto mb-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-2\",\n                                        children: \"No Analytics Data Yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"Start making API requests to see your analytics data here.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Analytics will appear once you begin using your API configurations.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Cost\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold text-white\",\n                                                                        children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    costTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm px-2 py-1 rounded flex items-center space-x-1 \".concat(costTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'),\n                                                                        children: [\n                                                                            costTrend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 478,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 480,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    costTrend.percentage.toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-48 relative bg-gray-800/50 rounded\",\n                                                children: timeSeriesData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 400 120\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"costGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#10b981\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#10b981\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 500,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 498,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            [\n                                                                ...Array(5)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                    x1: \"0\",\n                                                                    y1: i * 24,\n                                                                    x2: \"400\",\n                                                                    y2: i * 24,\n                                                                    stroke: \"#374151\",\n                                                                    strokeWidth: \"0.5\",\n                                                                    opacity: \"0.3\"\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                fill: \"none\",\n                                                                stroke: \"#10b981\",\n                                                                strokeWidth: \"2\",\n                                                                points: timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxCost = Math.max(...timeSeriesData.map((item)=>item.cost));\n                                                                    const y = 120 - d.cost / maxCost * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' ')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                fill: \"url(#costGradient)\",\n                                                                points: \"0,120 \".concat(timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxCost = Math.max(...timeSeriesData.map((item)=>item.cost));\n                                                                    const y = 120 - d.cost / maxCost * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' '), \" 400,120\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-8 w-8 mx-auto mb-2 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"No data available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Latency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold text-white\",\n                                                                        children: [\n                                                                            Math.round((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0),\n                                                                            \"ms\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    latencyTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm px-2 py-1 rounded flex items-center space-x-1 \".concat(latencyTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'),\n                                                                        children: [\n                                                                            latencyTrend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 568,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 570,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    latencyTrend.percentage.toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 572,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-48 relative bg-gray-800/50 rounded\",\n                                                children: timeSeriesData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 400 120\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"latencyGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#f59e0b\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#f59e0b\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 590,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            [\n                                                                ...Array(5)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                    x1: \"0\",\n                                                                    y1: i * 24,\n                                                                    x2: \"400\",\n                                                                    y2: i * 24,\n                                                                    stroke: \"#374151\",\n                                                                    strokeWidth: \"0.5\",\n                                                                    opacity: \"0.3\"\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                fill: \"none\",\n                                                                stroke: \"#f59e0b\",\n                                                                strokeWidth: \"2\",\n                                                                points: timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxLatency = Math.max(...timeSeriesData.map((item)=>item.latency || 0));\n                                                                    const y = 120 - (d.latency || 0) / Math.max(maxLatency, 1) * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' ')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 609,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                fill: \"url(#latencyGradient)\",\n                                                                points: \"0,120 \".concat(timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxLatency = Math.max(...timeSeriesData.map((item)=>item.latency || 0));\n                                                                    const y = 120 - (d.latency || 0) / Math.max(maxLatency, 1) * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' '), \" 400,120\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-8 w-8 mx-auto mb-2 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"No data available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Tokens Used\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400 mt-1\",\n                                                                children: \"March 28\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_tokens) || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 663,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"8.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-32 relative bg-gray-800/50 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 mb-4 text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-yellow-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 674,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Input Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 675,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 678,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Output Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Total Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 683,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 672,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-16\",\n                                                            children: [\n                                                                ...Array(20)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute w-1 h-1 rounded-full \".concat(i % 3 === 0 ? 'bg-yellow-500' : i % 3 === 1 ? 'bg-green-500' : 'bg-blue-500'),\n                                                                    style: {\n                                                                        left: \"\".concat(Math.random() * 90, \"%\"),\n                                                                        top: \"\".concat(Math.random() * 80, \"%\")\n                                                                    }\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 688,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 713,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 722,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"3.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data.slice(0, 5).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 text-sm text-gray-400 truncate\",\n                                                                children: provider.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 mx-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 bg-gray-800 rounded-full overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full rounded-full \".concat(index === 0 ? 'bg-pink-500' : index === 1 ? 'bg-purple-500' : index === 2 ? 'bg-cyan-500' : index === 3 ? 'bg-green-500' : 'bg-yellow-500'),\n                                                                        style: {\n                                                                            width: \"\".concat(provider.requests / ((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 1) * 100, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 736,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 w-12 text-right\",\n                                                                children: formatNumber(provider.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, provider.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Unique Users\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: (summary === null || summary === void 0 ? void 0 : summary.successful_requests) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"3.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 771,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 767,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-32 relative bg-gray-800/50 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 200 80\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"waveGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#8b5cf6\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 783,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#8b5cf6\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 784,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 782,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0,40 Q50,20 100,40 T200,40\",\n                                                                fill: \"none\",\n                                                                stroke: \"#8b5cf6\",\n                                                                strokeWidth: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0,40 Q50,20 100,40 T200,40 L200,80 L0,80 Z\",\n                                                                fill: \"url(#waveGradient)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 758,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'users' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 812,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Users Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 813,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"User analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 814,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 811,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'errors' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 820,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Error Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 821,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Error analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 822,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 819,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'cache' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 828,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Cache Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 829,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Cache analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 830,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 827,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'feedback' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 836,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Feedback Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 837,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Feedback analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 835,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'metadata' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_MagnifyingGlassIcon_ServerIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"Metadata Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 845,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Metadata analytics coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 846,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 843,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 410,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 296,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPageContent, \"V6GaieX1/66vfxScG50cnS5Gmxs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription\n    ];\n});\n_c1 = AnalyticsPageContent;\nfunction AnalyticsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-800 rounded w-1/3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 860,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(4)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-lg p-6 border border-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-800 rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 864,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-800 rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 865,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-800 rounded w-1/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 863,\n                                    columnNumber: 17\n                                }, void 0))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 861,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 859,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 858,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 857,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 874,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 856,\n        columnNumber: 5\n    }, this);\n}\n_c2 = AnalyticsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MetricCard\");\n$RefreshReg$(_c1, \"AnalyticsPageContent\");\n$RefreshReg$(_c2, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYW5hbHl0aWNzL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUU7QUFDdkI7QUFDYztBQW1CckI7QUE4Q3JDLG9CQUFvQjtBQUNwQixNQUFNb0IsZUFBZSxDQUFDQztJQUNwQixJQUFJQSxPQUFPLFNBQVMsT0FBTyxHQUE4QixPQUEzQixDQUFDQSxNQUFNLE9BQU0sRUFBR0MsT0FBTyxDQUFDLElBQUc7SUFDekQsSUFBSUQsT0FBTyxNQUFNLE9BQU8sR0FBMkIsT0FBeEIsQ0FBQ0EsTUFBTSxJQUFHLEVBQUdDLE9BQU8sQ0FBQyxJQUFHO0lBQ25ELE9BQU9ELElBQUlFLFFBQVE7QUFDckI7QUFFQSxNQUFNQyxpQkFBaUIsQ0FBQ0M7SUFDdEIsT0FBTyxJQUFJQyxLQUFLQyxZQUFZLENBQUMsU0FBUztRQUNwQ0MsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLHVCQUF1QjtRQUN2QkMsdUJBQXVCO0lBQ3pCLEdBQUdDLE1BQU0sQ0FBQ1A7QUFDWjtBQUVBLE1BQU1RLGlCQUFpQixDQUFDQyxTQUFpQkM7SUFDdkMsSUFBSUEsYUFBYSxHQUFHLE9BQU87UUFBRUMsWUFBWTtRQUFHQyxZQUFZSCxVQUFVO0lBQUU7SUFDcEUsTUFBTUUsYUFBYSxDQUFFRixVQUFVQyxRQUFPLElBQUtBLFdBQVk7SUFDdkQsT0FBTztRQUFFQyxZQUFZRSxLQUFLQyxHQUFHLENBQUNIO1FBQWFDLFlBQVlELGNBQWM7SUFBRTtBQUN6RTtBQUVBLHVCQUF1QjtBQUN2QixNQUFNSSxhQUF3QztRQUFDLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFQyxLQUFLLEVBQUVDLElBQUksRUFBRUMsUUFBUSxFQUFFO3lCQUNwRiw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEOztzQ0FDQyw4REFBQ0U7NEJBQUVELFdBQVU7c0NBQThCTjs7Ozs7O3NDQUMzQyw4REFBQ0s7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FBS0YsV0FBVTs4Q0FDYixPQUFPTCxVQUFVLFdBQVd0QixhQUFhc0IsU0FBU0E7Ozs7OztnQ0FFcERDLHVCQUNDLDhEQUFDTTtvQ0FBS0YsV0FBVyw0REFFaEIsT0FEQ0osTUFBTU4sVUFBVSxHQUFHLG1DQUFtQzs7d0NBRXJETSxNQUFNTixVQUFVLGlCQUNmLDhEQUFDdkIsd1RBQW1CQTs0Q0FBQ2lDLFdBQVU7Ozs7O3NFQUUvQiw4REFBQ2hDLHdUQUFxQkE7NENBQUNnQyxXQUFVOzs7Ozs7c0RBRW5DLDhEQUFDRTs7Z0RBQU1OLE1BQU1QLFVBQVUsQ0FBQ2QsT0FBTyxDQUFDO2dEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQUl4Q3VCLDBCQUFZLDhEQUFDRzs0QkFBRUQsV0FBVTtzQ0FBOEJGOzs7Ozs7Ozs7Ozs7OEJBRTFELDhEQUFDQztvQkFBSUMsV0FBVTs4QkFDWkg7Ozs7Ozs7Ozs7Ozs7Ozs7OztLQXpCSEo7QUErQk4sU0FBU1U7UUF3QkhDLG9CQTRGQUE7O0lBbkhKLE1BQU1DLFNBQVNoRCwwREFBU0E7SUFDeEIsTUFBTSxFQUFFK0MsWUFBWSxFQUFFLEdBQUc5Qyx1RUFBZUE7SUFFeEMsTUFBTSxDQUFDZ0QsZUFBZUMsaUJBQWlCLEdBQUd0RCwrQ0FBUUEsQ0FBdUI7SUFDekUsTUFBTSxDQUFDdUQsb0JBQW9CQyxzQkFBc0IsR0FBR3hELCtDQUFRQSxDQUF1QjtJQUNuRixNQUFNLENBQUN5RCxnQkFBZ0JDLGtCQUFrQixHQUFHMUQsK0NBQVFBLENBQW1CLEVBQUU7SUFDekUsTUFBTSxDQUFDMkQsZUFBZUMsaUJBQWlCLEdBQUc1RCwrQ0FBUUEsQ0FBb0IsRUFBRTtJQUN4RSxNQUFNLENBQUM2RCxTQUFTQyxXQUFXLEdBQUc5RCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUMrRCxPQUFPQyxTQUFTLEdBQUdoRSwrQ0FBUUEsQ0FBZ0I7SUFFbEQsVUFBVTtJQUNWLE1BQU0sQ0FBQ2lFLFdBQVdDLGFBQWEsR0FBR2xFLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ21FLGdCQUFnQkMsa0JBQWtCLEdBQUdwRSwrQ0FBUUEsQ0FBUztJQUM3RCxNQUFNLENBQUNxRSxXQUFXQyxhQUFhLEdBQUd0RSwrQ0FBUUEsQ0FBQztJQUUzQ0MsZ0RBQVNBOzBDQUFDO1lBQ1JzRTtRQUNGO3lDQUFHLEVBQUU7SUFFTHRFLGdEQUFTQTswQ0FBQztnQkFDSmtEO1lBQUosSUFBSUEseUJBQUFBLG9DQUFBQSxxQkFBQUEsYUFBY3FCLElBQUksY0FBbEJyQix5Q0FBQUEsbUJBQW9Cc0IsRUFBRSxFQUFFO2dCQUMxQkM7WUFDRjtRQUNGO3lDQUFHO1FBQUN2Qix5QkFBQUEsb0NBQUFBLHFCQUFBQSxhQUFjcUIsSUFBSSxjQUFsQnJCLHlDQUFBQSxtQkFBb0JzQixFQUFFO1FBQUVSO1FBQVdFO0tBQWU7SUFFdEQsTUFBTUkscUJBQXFCO1FBQ3pCLElBQUk7WUFDRixNQUFNSSxXQUFXLE1BQU1DLE1BQU07WUFDN0IsSUFBSUQsU0FBU0UsRUFBRSxFQUFFO2dCQUNmLE1BQU1DLFVBQVUsTUFBTUgsU0FBU0ksSUFBSTtnQkFDbkNuQixpQkFBaUJrQjtZQUNuQjtRQUNGLEVBQUUsT0FBT0UsS0FBSztZQUNaQyxRQUFRbEIsS0FBSyxDQUFDLDJCQUEyQmlCO1FBQzNDO0lBQ0Y7SUFFQSxNQUFNTixxQkFBcUJ4RSxrREFBV0E7Z0VBQUM7WUFDckMsSUFBSTtvQkFJdURpRCxvQkFnRHJDK0Isb0JBQ0R6QjtnQkFwRG5CSyxXQUFXO2dCQUNYRSxTQUFTO2dCQUVUaUIsUUFBUUUsR0FBRyxDQUFDLDZDQUE2Q2hDLHlCQUFBQSxvQ0FBQUEscUJBQUFBLGFBQWNxQixJQUFJLGNBQWxCckIseUNBQUFBLG1CQUFvQnNCLEVBQUU7Z0JBRS9FLHlCQUF5QjtnQkFDekIsTUFBTVcsU0FBUyxJQUFJQztnQkFDbkIsTUFBTUMsWUFBWSxJQUFJQztnQkFDdEJELFVBQVVFLE9BQU8sQ0FBQ0YsVUFBVUcsT0FBTyxLQUFLQyxTQUFTekI7Z0JBQ2pEbUIsT0FBT08sTUFBTSxDQUFDLGFBQWFMLFVBQVVNLFdBQVc7Z0JBRWhELElBQUl6QixnQkFBZ0I7b0JBQ2xCaUIsT0FBT08sTUFBTSxDQUFDLHFCQUFxQnhCO2dCQUNyQztnQkFFQWMsUUFBUUUsR0FBRyxDQUFDLHFDQUFxQ0MsT0FBTzdELFFBQVE7Z0JBRWhFLDhDQUE4QztnQkFDOUMsTUFBTXNFLGFBQWEsSUFBSVI7Z0JBQ3ZCLE1BQU1TLGdCQUFnQixJQUFJUDtnQkFDMUJPLGNBQWNOLE9BQU8sQ0FBQ00sY0FBY0wsT0FBTyxLQUFLQyxTQUFTekIsYUFBYTtnQkFDdEUsTUFBTThCLGNBQWMsSUFBSVI7Z0JBQ3hCUSxZQUFZUCxPQUFPLENBQUNPLFlBQVlOLE9BQU8sS0FBS0MsU0FBU3pCO2dCQUNyRDRCLFdBQVdGLE1BQU0sQ0FBQyxhQUFhRyxjQUFjRixXQUFXO2dCQUN4REMsV0FBV0YsTUFBTSxDQUFDLFdBQVdJLFlBQVlILFdBQVc7Z0JBRXBELElBQUl6QixnQkFBZ0I7b0JBQ2xCMEIsV0FBV0YsTUFBTSxDQUFDLHFCQUFxQnhCO2dCQUN6QztnQkFFQSx1QkFBdUI7Z0JBQ3ZCLE1BQU0sQ0FBQ1EsVUFBVXFCLGNBQWNDLG1CQUFtQixHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztvQkFDckV2QixNQUFNLDBCQUE0QyxPQUFsQlEsT0FBTzdELFFBQVEsSUFBRztvQkFDbERxRCxNQUFNLDBCQUFnRCxPQUF0QmlCLFdBQVd0RSxRQUFRLElBQUc7b0JBQ3REcUQsTUFBTSwwQkFBNEMsT0FBbEJRLE9BQU83RCxRQUFRLElBQUc7aUJBQ25EO2dCQUVEMEQsUUFBUUUsR0FBRyxDQUFDLGdDQUFnQ1IsU0FBU3lCLE1BQU07Z0JBRTNELElBQUksQ0FBQ3pCLFNBQVNFLEVBQUUsRUFBRTtvQkFDaEIsTUFBTXdCLFlBQVksTUFBTTFCLFNBQVMyQixJQUFJO29CQUNyQ3JCLFFBQVFsQixLQUFLLENBQUMsMEJBQTBCc0M7b0JBQ3hDLE1BQU0sSUFBSUUsTUFBTSxtQ0FBc0RGLE9BQW5CMUIsU0FBU3lCLE1BQU0sRUFBQyxLQUFhLE9BQVZDO2dCQUN4RTtnQkFFQSxNQUFNbkIsT0FBTyxNQUFNUCxTQUFTSSxJQUFJO2dCQUNoQyxNQUFNeUIsV0FBV1IsYUFBYW5CLEVBQUUsR0FBRyxNQUFNbUIsYUFBYWpCLElBQUksS0FBSztnQkFDL0QsTUFBTXRCLGlCQUFpQndDLG1CQUFtQnBCLEVBQUUsR0FBRyxNQUFNb0IsbUJBQW1CbEIsSUFBSSxLQUFLO2dCQUVqRkUsUUFBUUUsR0FBRyxDQUFDLDhCQUE4QjtvQkFDeENzQixTQUFTdkIsS0FBS3VCLE9BQU87b0JBQ3JCQyxrQkFBa0J4QixFQUFBQSxxQkFBQUEsS0FBS3lCLFlBQVksY0FBakJ6Qix5Q0FBQUEsbUJBQW1CMEIsTUFBTSxLQUFJO29CQUMvQ0MsaUJBQWlCcEQsQ0FBQUEsMkJBQUFBLHNDQUFBQSwrQkFBQUEsZUFBZ0JrRCxZQUFZLGNBQTVCbEQsbURBQUFBLDZCQUE4Qm1ELE1BQU0sS0FBSTtnQkFDM0Q7Z0JBRUF0RCxpQkFBaUI0QjtnQkFDakIxQixzQkFBc0JnRDtnQkFFdEIsSUFBSS9DLDJCQUFBQSxxQ0FBQUEsZUFBZ0JrRCxZQUFZLEVBQUU7b0JBQ2hDLE1BQU1HLHNCQUFzQnJELGVBQWVrRCxZQUFZLENBQUNJLEdBQUc7b0dBQUMsQ0FBQ0MsT0FBZTtnQ0FDMUVDLE1BQU1ELEtBQUtFLE1BQU0sSUFBSUYsS0FBS0csSUFBSTtnQ0FDOUJDLE1BQU1KLEtBQUtJLElBQUksSUFBSTtnQ0FDbkJDLFVBQVVMLEtBQUtLLFFBQVEsSUFBSTtnQ0FDM0JDLFFBQVEsQ0FBQ04sS0FBS08sWUFBWSxJQUFJLEtBQU1QLENBQUFBLEtBQUtRLGFBQWEsSUFBSTtnQ0FDMURDLFNBQVNULEtBQUtVLFdBQVcsSUFBSTs0QkFDL0I7O29CQUNBaEUsa0JBQWtCb0Q7b0JBQ2xCN0IsUUFBUUUsR0FBRyxDQUFDLHNDQUFzQzJCLG9CQUFvQkYsTUFBTSxFQUFFO2dCQUNoRjtZQUVGLEVBQUUsT0FBTzVCLEtBQVU7Z0JBQ2pCQyxRQUFRbEIsS0FBSyxDQUFDLG9DQUFvQ2lCO2dCQUNsRGhCLFNBQVNnQixJQUFJMkMsT0FBTztZQUN0QixTQUFVO2dCQUNSN0QsV0FBVztZQUNiO1FBQ0Y7K0RBQUc7UUFBQ1gseUJBQUFBLG9DQUFBQSxzQkFBQUEsYUFBY3FCLElBQUksY0FBbEJyQiwwQ0FBQUEsb0JBQW9Cc0IsRUFBRTtRQUFFUjtRQUFXRTtLQUFlO0lBRXRELGdCQUFnQjtJQUNoQixJQUFJTixTQUFTO1FBQ1gscUJBQ0UsOERBQUNmO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7c0NBQ2YsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNaO21DQUFJNkUsTUFBTTs2QkFBRyxDQUFDYixHQUFHLENBQUMsQ0FBQ2MsR0FBR0Msa0JBQ3JCLDhEQUFDaEY7b0NBQVlDLFdBQVU7O3NEQUNyQiw4REFBQ0Q7NENBQUlDLFdBQVU7Ozs7OztzREFDZiw4REFBQ0Q7NENBQUlDLFdBQVU7Ozs7OztzREFDZiw4REFBQ0Q7NENBQUlDLFdBQVU7Ozs7Ozs7bUNBSFArRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFXeEI7SUFFQSxjQUFjO0lBQ2QsSUFBSS9ELE9BQU87UUFDVCxxQkFDRSw4REFBQ2pCO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNnRjs0QkFBR2hGLFdBQVU7c0NBQThCOzs7Ozs7c0NBQzVDLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFFRCxXQUFVOzt3Q0FBb0I7d0NBQTBCZ0I7Ozs7Ozs7OENBQzNELDhEQUFDaUU7b0NBQ0NDLFNBQVN2RDtvQ0FDVDNCLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFRYjtJQUVBLE1BQU0wRCxVQUFVcEQsMEJBQUFBLG9DQUFBQSxjQUFlb0QsT0FBTztJQUN0QyxNQUFNeUIsa0JBQWtCM0UsK0JBQUFBLHlDQUFBQSxtQkFBb0JrRCxPQUFPO0lBRW5ELG1CQUFtQjtJQUNuQixNQUFNMEIsWUFBWUQsa0JBQWtCakcsZUFBZXdFLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBUzJCLFVBQVUsS0FBSSxHQUFHRixnQkFBZ0JFLFVBQVUsSUFBSTtJQUMzRyxNQUFNQyxlQUFlSCxrQkFBa0JqRyxlQUFld0UsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTNkIsY0FBYyxLQUFJLEdBQUdKLGdCQUFnQkksY0FBYyxJQUFJO0lBQ3RILE1BQU1DLGVBQWVMLGtCQUFrQmpHLGVBQWV3RSxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVMrQixlQUFlLEtBQUksR0FBR04sZ0JBQWdCTSxlQUFlLElBQUksS0FBSztJQUM3SCxNQUFNQyxlQUFlUCxrQkFBa0JqRyxlQUFld0UsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTaUMsWUFBWSxLQUFJLEdBQUdSLGdCQUFnQlEsWUFBWSxJQUFJO0lBRWxILHFCQUNFLDhEQUFDNUY7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNnRjt3Q0FBR2hGLFdBQVU7a0RBQW9DOzs7Ozs7a0RBQ2xELDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNpRjtnREFBT2pGLFdBQVU7MERBQXFFOzs7Ozs7MERBR3ZGLDhEQUFDaUY7Z0RBQU9qRixXQUFVOzBEQUFtRDs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUt6RSw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUN6Qyx3VEFBbUJBO2dEQUFDeUMsV0FBVTs7Ozs7OzBEQUMvQiw4REFBQzRGO2dEQUNDQyxNQUFLO2dEQUNMQyxhQUFZO2dEQUNaOUYsV0FBVTs7Ozs7Ozs7Ozs7O2tEQUdkLDhEQUFDK0Y7d0NBQ0NwRyxPQUFPdUI7d0NBQ1A4RSxVQUFVLENBQUNDLElBQU05RSxhQUFhOEUsRUFBRUMsTUFBTSxDQUFDdkcsS0FBSzt3Q0FDNUNLLFdBQVU7OzBEQUVWLDhEQUFDbUc7Z0RBQU94RyxPQUFNOzBEQUFJOzs7Ozs7MERBQ2xCLDhEQUFDd0c7Z0RBQU94RyxPQUFNOzBEQUFLOzs7Ozs7MERBQ25CLDhEQUFDd0c7Z0RBQU94RyxPQUFNOzBEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVE3Qiw4REFBQ0k7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNpRjtnQ0FDQ0MsU0FBUyxJQUFNM0QsYUFBYTtnQ0FDNUJ2QixXQUFXLGlFQUlWLE9BSENzQixjQUFjLGFBQ1Ysa0NBQ0E7O2tEQUdOLDhEQUFDeEQsd1RBQU9BO3dDQUFDa0MsV0FBVTs7Ozs7O2tEQUNuQiw4REFBQ0U7d0NBQUtGLFdBQVU7a0RBQXNCOzs7Ozs7Ozs7Ozs7MENBRXhDLDhEQUFDaUY7Z0NBQ0NDLFNBQVMsSUFBTTNELGFBQWE7Z0NBQzVCdkIsV0FBVyxpRUFJVixPQUhDc0IsY0FBYyxVQUNWLGtDQUNBOztrREFHTiw4REFBQzdELHdUQUFTQTt3Q0FBQ3VDLFdBQVU7Ozs7OztrREFDckIsOERBQUNFO3dDQUFLRixXQUFVO2tEQUFVOzs7Ozs7Ozs7Ozs7MENBRTVCLDhEQUFDaUY7Z0NBQ0NDLFNBQVMsSUFBTTNELGFBQWE7Z0NBQzVCdkIsV0FBVyxpRUFJVixPQUhDc0IsY0FBYyxXQUNWLGtDQUNBOztrREFHTiw4REFBQzVELHdUQUF1QkE7d0NBQUNzQyxXQUFVOzs7Ozs7a0RBQ25DLDhEQUFDRTt3Q0FBS0YsV0FBVTtrREFBVTs7Ozs7Ozs7Ozs7OzBDQUU1Qiw4REFBQ2lGO2dDQUNDQyxTQUFTLElBQU0zRCxhQUFhO2dDQUM1QnZCLFdBQVcsaUVBSVYsT0FIQ3NCLGNBQWMsVUFDVixrQ0FDQTs7a0RBR04sOERBQUMzRCx5VEFBVUE7d0NBQUNxQyxXQUFVOzs7Ozs7a0RBQ3RCLDhEQUFDRTt3Q0FBS0YsV0FBVTtrREFBVTs7Ozs7Ozs7Ozs7OzBDQUU1Qiw4REFBQ2lGO2dDQUNDQyxTQUFTLElBQU0zRCxhQUFhO2dDQUM1QnZCLFdBQVcsaUVBSVYsT0FIQ3NCLGNBQWMsYUFDVixrQ0FDQTs7a0RBR04sOERBQUMxRCx5VEFBdUJBO3dDQUFDb0MsV0FBVTs7Ozs7O2tEQUNuQyw4REFBQ0U7d0NBQUtGLFdBQVU7a0RBQVU7Ozs7Ozs7Ozs7OzswQ0FFNUIsOERBQUNpRjtnQ0FDQ0MsU0FBUyxJQUFNM0QsYUFBYTtnQ0FDNUJ2QixXQUFXLGlFQUlWLE9BSENzQixjQUFjLGFBQ1Ysa0NBQ0E7O2tEQUdOLDhEQUFDekQseVRBQWdCQTt3Q0FBQ21DLFdBQVU7Ozs7OztrREFDNUIsOERBQUNFO3dDQUFLRixXQUFVO2tEQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9sQyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O29CQUNac0IsY0FBYyw0QkFDYiw4REFBQ3ZCO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUViLDhEQUFDUDt3Q0FDQ0MsT0FBTTt3Q0FDTkMsT0FBTytELENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBUzZCLGNBQWMsS0FBSTt3Q0FDbEMzRixPQUFPMEY7d0NBQ1B6RixvQkFBTSw4REFBQ3JDLHlUQUFZQTs0Q0FBQ3dDLFdBQVU7Ozs7Ozs7Ozs7O2tEQUloQyw4REFBQ1A7d0NBQ0NDLE9BQU07d0NBQ05DLE9BQU8sR0FBNkMsT0FBMUNKLEtBQUs2RyxLQUFLLENBQUMxQyxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVMrQixlQUFlLEtBQUksSUFBRzt3Q0FDcEQ3RixPQUFPNEY7d0NBQ1AzRixvQkFBTSw4REFBQzVCLHlUQUFTQTs0Q0FBQytCLFdBQVU7Ozs7Ozs7Ozs7O2tEQUk3Qiw4REFBQ1A7d0NBQ0NDLE9BQU07d0NBQ05DLE9BQU8sR0FBMkMsT0FBeEMsQ0FBQytELENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU2lDLFlBQVksS0FBSSxHQUFHcEgsT0FBTyxDQUFDLElBQUc7d0NBQ2xEcUIsT0FBTzhGO3dDQUNQN0Ysb0JBQU0sOERBQUNqQyx5VEFBdUJBOzRDQUFDb0MsV0FBVTs7Ozs7Ozs7Ozs7a0RBSTNDLDhEQUFDUDt3Q0FDQ0MsT0FBTTt3Q0FDTkMsT0FBT2xCLGVBQWVpRixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVMyQixVQUFVLEtBQUk7d0NBQzdDekYsT0FBT3dGO3dDQUNQdkYsb0JBQU0sOERBQUMzQix5VEFBa0JBOzRDQUFDOEIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBS3RDLEVBQUMwRCxXQUFXQSxRQUFRNkIsY0FBYyxLQUFLLG9CQUN2Qyw4REFBQ3hGO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3hDLHlUQUFZQTt3Q0FBQ3dDLFdBQVU7Ozs7OztrREFDeEIsOERBQUNxRzt3Q0FBR3JHLFdBQVU7a0RBQXdDOzs7Ozs7a0RBQ3RELDhEQUFDQzt3Q0FBRUQsV0FBVTtrREFBcUI7Ozs7OztrREFHbEMsOERBQUNDO3dDQUFFRCxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7OzBDQU96Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUViLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7OzBFQUNDLDhEQUFDc0c7Z0VBQUdyRyxXQUFVOzBFQUFtQzs7Ozs7OzBFQUNqRCw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRTt3RUFBS0YsV0FBVTtrRkFDYnZCLGVBQWVpRixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVMyQixVQUFVLEtBQUk7Ozs7OztvRUFFeENELDJCQUNDLDhEQUFDbEY7d0VBQUtGLFdBQVcseURBRWhCLE9BRENvRixVQUFVOUYsVUFBVSxHQUFHLCtCQUErQjs7NEVBRXJEOEYsVUFBVTlGLFVBQVUsaUJBQ25CLDhEQUFDdkIsd1RBQW1CQTtnRkFBQ2lDLFdBQVU7Ozs7O3FHQUUvQiw4REFBQ2hDLHdUQUFxQkE7Z0ZBQUNnQyxXQUFVOzs7Ozs7MEZBRW5DLDhEQUFDRTs7b0ZBQU1rRixVQUFVL0YsVUFBVSxDQUFDZCxPQUFPLENBQUM7b0ZBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSy9DLDhEQUFDd0I7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUM5Qix5VEFBa0JBOzREQUFDOEIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBS2xDLDhEQUFDRDtnREFBSUMsV0FBVTswREFDWlUsZUFBZW1ELE1BQU0sR0FBRyxrQkFDdkIsOERBQUM5RDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ3NHO3dEQUFJdEcsV0FBVTt3REFBZ0J1RyxTQUFROzswRUFDckMsOERBQUNDOzBFQUNDLDRFQUFDQztvRUFBZS9FLElBQUc7b0VBQWVnRixJQUFHO29FQUFLQyxJQUFHO29FQUFLQyxJQUFHO29FQUFLQyxJQUFHOztzRkFDM0QsOERBQUNDOzRFQUFLQyxRQUFPOzRFQUFLQyxXQUFVOzRFQUFVQyxhQUFZOzs7Ozs7c0ZBQ2xELDhEQUFDSDs0RUFBS0MsUUFBTzs0RUFBT0MsV0FBVTs0RUFBVUMsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7NERBS3ZEO21FQUFJcEMsTUFBTTs2REFBRyxDQUFDYixHQUFHLENBQUMsQ0FBQ2MsR0FBR0Msa0JBQ3JCLDhEQUFDbUM7b0VBRUNSLElBQUc7b0VBQ0hDLElBQUk1QixJQUFJO29FQUNSNkIsSUFBRztvRUFDSEMsSUFBSTlCLElBQUk7b0VBQ1JvQyxRQUFPO29FQUNQQyxhQUFZO29FQUNaQyxTQUFRO21FQVBIdEM7Ozs7OzBFQVlULDhEQUFDdUM7Z0VBQ0NDLE1BQUs7Z0VBQ0xKLFFBQU87Z0VBQ1BDLGFBQVk7Z0VBQ1pJLFFBQVE5RyxlQUFlc0QsR0FBRyxDQUFDLENBQUN5RCxHQUFHMUM7b0VBQzdCLE1BQU0yQyxJQUFJLElBQUtuSSxLQUFLb0ksR0FBRyxDQUFDakgsZUFBZW1ELE1BQU0sR0FBRyxHQUFHLEtBQU07b0VBQ3pELE1BQU0rRCxVQUFVckksS0FBS29JLEdBQUcsSUFBSWpILGVBQWVzRCxHQUFHLENBQUNDLENBQUFBLE9BQVFBLEtBQUtJLElBQUk7b0VBQ2hFLE1BQU13RCxJQUFJLE1BQU0sRUFBR3hELElBQUksR0FBR3VELFVBQVc7b0VBQ3JDLE9BQU8sR0FBUUMsT0FBTEgsR0FBRSxLQUFLLE9BQUZHO2dFQUNqQixHQUFHQyxJQUFJLENBQUM7Ozs7OzswRUFJViw4REFBQ0M7Z0VBQ0NSLE1BQUs7Z0VBQ0xDLFFBQVEsU0FLSyxPQUxJOUcsZUFBZXNELEdBQUcsQ0FBQyxDQUFDeUQsR0FBRzFDO29FQUN0QyxNQUFNMkMsSUFBSSxJQUFLbkksS0FBS29JLEdBQUcsQ0FBQ2pILGVBQWVtRCxNQUFNLEdBQUcsR0FBRyxLQUFNO29FQUN6RCxNQUFNK0QsVUFBVXJJLEtBQUtvSSxHQUFHLElBQUlqSCxlQUFlc0QsR0FBRyxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLSSxJQUFJO29FQUNoRSxNQUFNd0QsSUFBSSxNQUFNLEVBQUd4RCxJQUFJLEdBQUd1RCxVQUFXO29FQUNyQyxPQUFPLEdBQVFDLE9BQUxILEdBQUUsS0FBSyxPQUFGRztnRUFDakIsR0FBR0MsSUFBSSxDQUFDLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7eUVBS25CLDhEQUFDL0g7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ3hDLHlUQUFZQTtnRUFBQ3dDLFdBQVU7Ozs7OzswRUFDeEIsOERBQUNDO2dFQUFFRCxXQUFVOzBFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFRL0MsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDs7MEVBQ0MsOERBQUNzRztnRUFBR3JHLFdBQVU7MEVBQW1DOzs7Ozs7MEVBQ2pELDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNFO3dFQUFLRixXQUFVOzs0RUFDYlQsS0FBSzZHLEtBQUssQ0FBQzFDLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBUytCLGVBQWUsS0FBSTs0RUFBRzs7Ozs7OztvRUFFNUNELDhCQUNDLDhEQUFDdEY7d0VBQUtGLFdBQVcseURBRWhCLE9BREN3RixhQUFhbEcsVUFBVSxHQUFHLCtCQUErQjs7NEVBRXhEa0csYUFBYWxHLFVBQVUsaUJBQ3RCLDhEQUFDdkIsd1RBQW1CQTtnRkFBQ2lDLFdBQVU7Ozs7O3FHQUUvQiw4REFBQ2hDLHdUQUFxQkE7Z0ZBQUNnQyxXQUFVOzs7Ozs7MEZBRW5DLDhEQUFDRTs7b0ZBQU1zRixhQUFhbkcsVUFBVSxDQUFDZCxPQUFPLENBQUM7b0ZBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBS2xELDhEQUFDd0I7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUMvQix5VEFBU0E7NERBQUMrQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzswREFLekIsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNaVSxlQUFlbUQsTUFBTSxHQUFHLGtCQUN2Qiw4REFBQzlEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDc0c7d0RBQUl0RyxXQUFVO3dEQUFnQnVHLFNBQVE7OzBFQUNyQyw4REFBQ0M7MEVBQ0MsNEVBQUNDO29FQUFlL0UsSUFBRztvRUFBa0JnRixJQUFHO29FQUFLQyxJQUFHO29FQUFLQyxJQUFHO29FQUFLQyxJQUFHOztzRkFDOUQsOERBQUNDOzRFQUFLQyxRQUFPOzRFQUFLQyxXQUFVOzRFQUFVQyxhQUFZOzs7Ozs7c0ZBQ2xELDhEQUFDSDs0RUFBS0MsUUFBTzs0RUFBT0MsV0FBVTs0RUFBVUMsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7NERBS3ZEO21FQUFJcEMsTUFBTTs2REFBRyxDQUFDYixHQUFHLENBQUMsQ0FBQ2MsR0FBR0Msa0JBQ3JCLDhEQUFDbUM7b0VBRUNSLElBQUc7b0VBQ0hDLElBQUk1QixJQUFJO29FQUNSNkIsSUFBRztvRUFDSEMsSUFBSTlCLElBQUk7b0VBQ1JvQyxRQUFPO29FQUNQQyxhQUFZO29FQUNaQyxTQUFRO21FQVBIdEM7Ozs7OzBFQVlULDhEQUFDdUM7Z0VBQ0NDLE1BQUs7Z0VBQ0xKLFFBQU87Z0VBQ1BDLGFBQVk7Z0VBQ1pJLFFBQVE5RyxlQUFlc0QsR0FBRyxDQUFDLENBQUN5RCxHQUFHMUM7b0VBQzdCLE1BQU0yQyxJQUFJLElBQUtuSSxLQUFLb0ksR0FBRyxDQUFDakgsZUFBZW1ELE1BQU0sR0FBRyxHQUFHLEtBQU07b0VBQ3pELE1BQU1tRSxhQUFhekksS0FBS29JLEdBQUcsSUFBSWpILGVBQWVzRCxHQUFHLENBQUNDLENBQUFBLE9BQVFBLEtBQUtTLE9BQU8sSUFBSTtvRUFDMUUsTUFBTW1ELElBQUksTUFBTSxDQUFFSixFQUFFL0MsT0FBTyxJQUFJLEtBQUtuRixLQUFLb0ksR0FBRyxDQUFDSyxZQUFZLEtBQU07b0VBQy9ELE9BQU8sR0FBUUgsT0FBTEgsR0FBRSxLQUFLLE9BQUZHO2dFQUNqQixHQUFHQyxJQUFJLENBQUM7Ozs7OzswRUFJViw4REFBQ0M7Z0VBQ0NSLE1BQUs7Z0VBQ0xDLFFBQVEsU0FLSyxPQUxJOUcsZUFBZXNELEdBQUcsQ0FBQyxDQUFDeUQsR0FBRzFDO29FQUN0QyxNQUFNMkMsSUFBSSxJQUFLbkksS0FBS29JLEdBQUcsQ0FBQ2pILGVBQWVtRCxNQUFNLEdBQUcsR0FBRyxLQUFNO29FQUN6RCxNQUFNbUUsYUFBYXpJLEtBQUtvSSxHQUFHLElBQUlqSCxlQUFlc0QsR0FBRyxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLUyxPQUFPLElBQUk7b0VBQzFFLE1BQU1tRCxJQUFJLE1BQU0sQ0FBRUosRUFBRS9DLE9BQU8sSUFBSSxLQUFLbkYsS0FBS29JLEdBQUcsQ0FBQ0ssWUFBWSxLQUFNO29FQUMvRCxPQUFPLEdBQVFILE9BQUxILEdBQUUsS0FBSyxPQUFGRztnRUFDakIsR0FBR0MsSUFBSSxDQUFDLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7eUVBS25CLDhEQUFDL0g7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQy9CLHlUQUFTQTtnRUFBQytCLFdBQVU7Ozs7OzswRUFDckIsOERBQUNDO2dFQUFFRCxXQUFVOzBFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FTakQsOERBQUNEO2dDQUFJQyxXQUFVOztrREFFYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEOzswRUFDQyw4REFBQ3NHO2dFQUFHckcsV0FBVTswRUFBbUM7Ozs7OzswRUFDakQsOERBQUNDO2dFQUFFRCxXQUFVOzBFQUE2Qjs7Ozs7Ozs7Ozs7O2tFQUU1Qyw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUM3Qix5VEFBV0E7NERBQUM2QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzswREFHM0IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0U7d0RBQUtGLFdBQVU7a0VBQ2IzQixhQUFhcUYsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTdUUsWUFBWSxLQUFJOzs7Ozs7a0VBRXpDLDhEQUFDL0g7d0RBQUtGLFdBQVU7OzBFQUNkLDhEQUFDakMsd1RBQW1CQTtnRUFBQ2lDLFdBQVU7Ozs7OzswRUFDL0IsOERBQUNFOzBFQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBS1YsOERBQUNIO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTs7c0VBRWIsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7Ozs7O3NGQUNmLDhEQUFDRTs0RUFBS0YsV0FBVTtzRkFBZ0I7Ozs7Ozs7Ozs7Ozs4RUFFbEMsOERBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ0Q7NEVBQUlDLFdBQVU7Ozs7OztzRkFDZiw4REFBQ0U7NEVBQUtGLFdBQVU7c0ZBQWdCOzs7Ozs7Ozs7Ozs7OEVBRWxDLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVOzs7Ozs7c0ZBQ2YsOERBQUNFOzRFQUFLRixXQUFVO3NGQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQUtwQyw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQ1o7bUVBQUk2RSxNQUFNOzZEQUFJLENBQUNiLEdBQUcsQ0FBQyxDQUFDYyxHQUFHQyxrQkFDdEIsOERBQUNoRjtvRUFFQ0MsV0FBVyxpQ0FHVixPQUZDK0UsSUFBSSxNQUFNLElBQUksa0JBQ2RBLElBQUksTUFBTSxJQUFJLGlCQUFpQjtvRUFFakNsRyxPQUFPO3dFQUNMcUosTUFBTSxHQUFzQixPQUFuQjNJLEtBQUs0SSxNQUFNLEtBQUssSUFBRzt3RUFDNUJDLEtBQUssR0FBc0IsT0FBbkI3SSxLQUFLNEksTUFBTSxLQUFLLElBQUc7b0VBQzdCO21FQVJLcEQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFpQmpCLDhEQUFDaEY7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO2tFQUNDLDRFQUFDc0c7NERBQUdyRyxXQUFVO3NFQUFtQzs7Ozs7Ozs7Ozs7a0VBRW5ELDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQzVCLHlUQUFZQTs0REFBQzRCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUc1Qiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRTt3REFBS0YsV0FBVTtrRUFDYjNCLGFBQWFxRixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVM2QixjQUFjLEtBQUk7Ozs7OztrRUFFM0MsOERBQUNyRjt3REFBS0YsV0FBVTs7MEVBQ2QsOERBQUNqQyx3VEFBbUJBO2dFQUFDaUMsV0FBVTs7Ozs7OzBFQUMvQiw4REFBQ0U7MEVBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFLViw4REFBQ0g7Z0RBQUlDLFdBQVU7MERBQ1pNLDBCQUFBQSxvQ0FBQUEsY0FBZXNELFlBQVksQ0FBQ3lFLEtBQUssQ0FBQyxHQUFHLEdBQUdyRSxHQUFHLENBQUMsQ0FBQ3NFLFVBQVVDLHNCQUN0RCw4REFBQ3hJO3dEQUF3QkMsV0FBVTs7MEVBQ2pDLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFDWnNJLFNBQVNsRSxJQUFJOzs7Ozs7MEVBRWhCLDhEQUFDckU7Z0VBQUlDLFdBQVU7MEVBQ2IsNEVBQUNEO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDRDt3RUFDQ0MsV0FBVyx1QkFLVixPQUpDdUksVUFBVSxJQUFJLGdCQUNkQSxVQUFVLElBQUksa0JBQ2RBLFVBQVUsSUFBSSxnQkFDZEEsVUFBVSxJQUFJLGlCQUFpQjt3RUFFakMxSixPQUFPOzRFQUNMMkosT0FBTyxHQUE4RCxPQUEzRCxTQUFVbEUsUUFBUSxHQUFJWixDQUFBQSxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVM2QixjQUFjLEtBQUksS0FBTSxLQUFJO3dFQUN2RTs7Ozs7Ozs7Ozs7Ozs7OzswRUFJTiw4REFBQ3hGO2dFQUFJQyxXQUFVOzBFQUNaM0IsYUFBYWlLLFNBQVNoRSxRQUFROzs7Ozs7O3VEQXBCekJnRSxTQUFTbEUsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztrREE0QjdCLDhEQUFDckU7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO2tFQUNDLDRFQUFDc0c7NERBQUdyRyxXQUFVO3NFQUFtQzs7Ozs7Ozs7Ozs7a0VBRW5ELDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ3ZDLHdUQUFTQTs0REFBQ3VDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUd6Qiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRTt3REFBS0YsV0FBVTtrRUFDYjBELENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBUytFLG1CQUFtQixLQUFJOzs7Ozs7a0VBRW5DLDhEQUFDdkk7d0RBQUtGLFdBQVU7OzBFQUNkLDhEQUFDakMsd1RBQW1CQTtnRUFBQ2lDLFdBQVU7Ozs7OzswRUFDL0IsOERBQUNFOzBFQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBS1YsOERBQUNIO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ3NHO3dEQUFJdEcsV0FBVTt3REFBZ0J1RyxTQUFROzswRUFDckMsOERBQUNDOzBFQUNDLDRFQUFDQztvRUFBZS9FLElBQUc7b0VBQWVnRixJQUFHO29FQUFLQyxJQUFHO29FQUFLQyxJQUFHO29FQUFLQyxJQUFHOztzRkFDM0QsOERBQUNDOzRFQUFLQyxRQUFPOzRFQUFLQyxXQUFVOzRFQUFVQyxhQUFZOzs7Ozs7c0ZBQ2xELDhEQUFDSDs0RUFBS0MsUUFBTzs0RUFBT0MsV0FBVTs0RUFBVUMsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBS3hELDhEQUFDeUI7Z0VBQ0NqQixHQUFFO2dFQUNGRixNQUFLO2dFQUNMSixRQUFPO2dFQUNQQyxhQUFZOzs7Ozs7MEVBSWQsOERBQUNzQjtnRUFDQ2pCLEdBQUU7Z0VBQ0ZGLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBV3BCakcsY0FBYyx5QkFDYiw4REFBQ3ZCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3ZDLHdUQUFTQTtnQ0FBQ3VDLFdBQVU7Ozs7OzswQ0FDckIsOERBQUNxRztnQ0FBR3JHLFdBQVU7MENBQXdDOzs7Ozs7MENBQ3RELDhEQUFDQztnQ0FBRUQsV0FBVTswQ0FBZ0I7Ozs7Ozs7Ozs7OztvQkFJaENzQixjQUFjLDBCQUNiLDhEQUFDdkI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDdEMsd1RBQXVCQTtnQ0FBQ3NDLFdBQVU7Ozs7OzswQ0FDbkMsOERBQUNxRztnQ0FBR3JHLFdBQVU7MENBQXdDOzs7Ozs7MENBQ3RELDhEQUFDQztnQ0FBRUQsV0FBVTswQ0FBZ0I7Ozs7Ozs7Ozs7OztvQkFJaENzQixjQUFjLHlCQUNiLDhEQUFDdkI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDckMseVRBQVVBO2dDQUFDcUMsV0FBVTs7Ozs7OzBDQUN0Qiw4REFBQ3FHO2dDQUFHckcsV0FBVTswQ0FBd0M7Ozs7OzswQ0FDdEQsOERBQUNDO2dDQUFFRCxXQUFVOzBDQUFnQjs7Ozs7Ozs7Ozs7O29CQUloQ3NCLGNBQWMsNEJBQ2IsOERBQUN2Qjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNwQyx5VEFBdUJBO2dDQUFDb0MsV0FBVTs7Ozs7OzBDQUNuQyw4REFBQ3FHO2dDQUFHckcsV0FBVTswQ0FBd0M7Ozs7OzswQ0FDdEQsOERBQUNDO2dDQUFFRCxXQUFVOzBDQUFnQjs7Ozs7Ozs7Ozs7O29CQUloQ3NCLGNBQWMsNEJBQ2IsOERBQUN2Qjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNuQyx5VEFBZ0JBO2dDQUFDbUMsV0FBVTs7Ozs7OzBDQUM1Qiw4REFBQ3FHO2dDQUFHckcsV0FBVTswQ0FBd0M7Ozs7OzswQ0FDdEQsOERBQUNDO2dDQUFFRCxXQUFVOzBDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXpDO0dBeHRCU0c7O1FBQ1E5QyxzREFBU0E7UUFDQ0MsbUVBQWVBOzs7TUFGakM2QztBQTB0Qk0sU0FBU3dJO0lBQ3RCLHFCQUNFLDhEQUFDdkwsMkNBQVFBO1FBQUN3TCx3QkFDUiw4REFBQzdJO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7c0NBQ2YsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNaO21DQUFJNkUsTUFBTTs2QkFBRyxDQUFDYixHQUFHLENBQUMsQ0FBQ2MsR0FBR0Msa0JBQ3JCLDhEQUFDaEY7b0NBQVlDLFdBQVU7O3NEQUNyQiw4REFBQ0Q7NENBQUlDLFdBQVU7Ozs7OztzREFDZiw4REFBQ0Q7NENBQUlDLFdBQVU7Ozs7OztzREFDZiw4REFBQ0Q7NENBQUlDLFdBQVU7Ozs7Ozs7bUNBSFArRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0JBV3BCLDRFQUFDNUU7Ozs7Ozs7Ozs7QUFHUDtNQXZCd0J3SSIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGFwcFxcYW5hbHl0aWNzXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrLCBTdXNwZW5zZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyB1c2VTdWJzY3JpcHRpb24gfSBmcm9tICdAL2hvb2tzL3VzZVN1YnNjcmlwdGlvbic7XG5pbXBvcnQge1xuICBNYWduaWZ5aW5nR2xhc3NJY29uLFxuICBDaGFydEJhckljb24sXG4gIFVzZXJzSWNvbixcbiAgRXhjbGFtYXRpb25UcmlhbmdsZUljb24sXG4gIFNlcnZlckljb24sXG4gIENoYXRCdWJibGVMZWZ0UmlnaHRJY29uLFxuICBEb2N1bWVudFRleHRJY29uLFxuICBFeWVJY29uLFxuICBDYWxlbmRhckljb24sXG4gIEFycm93VHJlbmRpbmdVcEljb24sXG4gIEFycm93VHJlbmRpbmdEb3duSWNvbixcbiAgQ2xvY2tJY29uLFxuICBDdXJyZW5jeURvbGxhckljb24sXG4gIENwdUNoaXBJY29uLFxuICBDaGVja0NpcmNsZUljb24sXG4gIEJvbHRJY29uLFxuICBHbG9iZUFsdEljb25cbn0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcblxuaW50ZXJmYWNlIEFuYWx5dGljc1N1bW1hcnkge1xuICB0b3RhbF9yZXF1ZXN0czogbnVtYmVyO1xuICBzdWNjZXNzZnVsX3JlcXVlc3RzOiBudW1iZXI7XG4gIHN1Y2Nlc3NfcmF0ZTogbnVtYmVyO1xuICB0b3RhbF9jb3N0OiBudW1iZXI7XG4gIHRvdGFsX2lucHV0X3Rva2VuczogbnVtYmVyO1xuICB0b3RhbF9vdXRwdXRfdG9rZW5zOiBudW1iZXI7XG4gIHRvdGFsX3Rva2VuczogbnVtYmVyO1xuICBhdmVyYWdlX2Nvc3RfcGVyX3JlcXVlc3Q6IG51bWJlcjtcbiAgYXZlcmFnZV9sYXRlbmN5PzogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgQW5hbHl0aWNzRGF0YSB7XG4gIHN1bW1hcnk6IEFuYWx5dGljc1N1bW1hcnk7XG4gIGdyb3VwZWRfZGF0YTogYW55W107XG4gIGZpbHRlcnM6IGFueTtcbn1cblxuaW50ZXJmYWNlIFRpbWVTZXJpZXNEYXRhIHtcbiAgZGF0ZTogc3RyaW5nO1xuICBjb3N0OiBudW1iZXI7XG4gIHJlcXVlc3RzOiBudW1iZXI7XG4gIHRva2VuczogbnVtYmVyO1xuICBsYXRlbmN5PzogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgQ3VzdG9tQXBpQ29uZmlnIHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgVHJlbmREYXRhIHtcbiAgcGVyY2VudGFnZTogbnVtYmVyO1xuICBpc1Bvc2l0aXZlOiBib29sZWFuO1xufVxuXG5pbnRlcmZhY2UgTWV0cmljQ2FyZFByb3BzIHtcbiAgdGl0bGU6IHN0cmluZztcbiAgdmFsdWU6IHN0cmluZyB8IG51bWJlcjtcbiAgdHJlbmQ/OiBUcmVuZERhdGE7XG4gIGljb246IFJlYWN0LlJlYWN0Tm9kZTtcbiAgc3VidGl0bGU/OiBzdHJpbmc7XG59XG5cbi8vIFV0aWxpdHkgZnVuY3Rpb25zXG5jb25zdCBmb3JtYXROdW1iZXIgPSAobnVtOiBudW1iZXIpOiBzdHJpbmcgPT4ge1xuICBpZiAobnVtID49IDEwMDAwMDApIHJldHVybiBgJHsobnVtIC8gMTAwMDAwMCkudG9GaXhlZCgxKX1NYDtcbiAgaWYgKG51bSA+PSAxMDAwKSByZXR1cm4gYCR7KG51bSAvIDEwMDApLnRvRml4ZWQoMSl9S2A7XG4gIHJldHVybiBudW0udG9TdHJpbmcoKTtcbn07XG5cbmNvbnN0IGZvcm1hdEN1cnJlbmN5ID0gKGFtb3VudDogbnVtYmVyKTogc3RyaW5nID0+IHtcbiAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnZW4tVVMnLCB7XG4gICAgc3R5bGU6ICdjdXJyZW5jeScsXG4gICAgY3VycmVuY3k6ICdVU0QnLFxuICAgIG1pbmltdW1GcmFjdGlvbkRpZ2l0czogMixcbiAgICBtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDRcbiAgfSkuZm9ybWF0KGFtb3VudCk7XG59O1xuXG5jb25zdCBjYWxjdWxhdGVUcmVuZCA9IChjdXJyZW50OiBudW1iZXIsIHByZXZpb3VzOiBudW1iZXIpOiBUcmVuZERhdGEgPT4ge1xuICBpZiAocHJldmlvdXMgPT09IDApIHJldHVybiB7IHBlcmNlbnRhZ2U6IDAsIGlzUG9zaXRpdmU6IGN1cnJlbnQgPiAwIH07XG4gIGNvbnN0IHBlcmNlbnRhZ2UgPSAoKGN1cnJlbnQgLSBwcmV2aW91cykgLyBwcmV2aW91cykgKiAxMDA7XG4gIHJldHVybiB7IHBlcmNlbnRhZ2U6IE1hdGguYWJzKHBlcmNlbnRhZ2UpLCBpc1Bvc2l0aXZlOiBwZXJjZW50YWdlID49IDAgfTtcbn07XG5cbi8vIE1ldHJpY0NhcmQgQ29tcG9uZW50XG5jb25zdCBNZXRyaWNDYXJkOiBSZWFjdC5GQzxNZXRyaWNDYXJkUHJvcHM+ID0gKHsgdGl0bGUsIHZhbHVlLCB0cmVuZCwgaWNvbiwgc3VidGl0bGUgfSkgPT4gKFxuICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwLzUwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC1sZyBwLTYgYm9yZGVyIGJvcmRlci1ncmF5LTgwMC81MCBob3Zlcjpib3JkZXItZ3JheS03MDAvNTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCI+XG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgPGRpdj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwIG1iLTFcIj57dGl0bGV9PC9wPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICB7dHlwZW9mIHZhbHVlID09PSAnbnVtYmVyJyA/IGZvcm1hdE51bWJlcih2YWx1ZSkgOiB2YWx1ZX1cbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAge3RyZW5kICYmIChcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQtc20gcHgtMiBweS0xIHJvdW5kZWQtbWQgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xICR7XG4gICAgICAgICAgICAgIHRyZW5kLmlzUG9zaXRpdmUgPyAndGV4dC1ncmVlbi00MDAgYmctZ3JlZW4tNDAwLzEwJyA6ICd0ZXh0LXJlZC00MDAgYmctcmVkLTQwMC8xMCdcbiAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAge3RyZW5kLmlzUG9zaXRpdmUgPyAoXG4gICAgICAgICAgICAgICAgPEFycm93VHJlbmRpbmdVcEljb24gY2xhc3NOYW1lPVwidy0zIGgtM1wiIC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPEFycm93VHJlbmRpbmdEb3duSWNvbiBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPHNwYW4+e3RyZW5kLnBlcmNlbnRhZ2UudG9GaXhlZCgxKX0lPC9zcGFuPlxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgICB7c3VidGl0bGUgJiYgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTFcIj57c3VidGl0bGV9PC9wPn1cbiAgICAgIDwvZGl2PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgIHtpY29ufVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIDwvZGl2PlxuKTtcblxuZnVuY3Rpb24gQW5hbHl0aWNzUGFnZUNvbnRlbnQoKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCB7IHN1YnNjcmlwdGlvbiB9ID0gdXNlU3Vic2NyaXB0aW9uKCk7XG4gIFxuICBjb25zdCBbYW5hbHl0aWNzRGF0YSwgc2V0QW5hbHl0aWNzRGF0YV0gPSB1c2VTdGF0ZTxBbmFseXRpY3NEYXRhIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtwcmV2aW91c1BlcmlvZERhdGEsIHNldFByZXZpb3VzUGVyaW9kRGF0YV0gPSB1c2VTdGF0ZTxBbmFseXRpY3NEYXRhIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFt0aW1lU2VyaWVzRGF0YSwgc2V0VGltZVNlcmllc0RhdGFdID0gdXNlU3RhdGU8VGltZVNlcmllc0RhdGFbXT4oW10pO1xuICBjb25zdCBbY3VzdG9tQ29uZmlncywgc2V0Q3VzdG9tQ29uZmlnc10gPSB1c2VTdGF0ZTxDdXN0b21BcGlDb25maWdbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcblxuICAvLyBGaWx0ZXJzXG4gIGNvbnN0IFt0aW1lUmFuZ2UsIHNldFRpbWVSYW5nZV0gPSB1c2VTdGF0ZSgnMzAnKTtcbiAgY29uc3QgW3NlbGVjdGVkQ29uZmlnLCBzZXRTZWxlY3RlZENvbmZpZ10gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlKCdvdmVydmlldycpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hDdXN0b21Db25maWdzKCk7XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzdWJzY3JpcHRpb24/LnVzZXI/LmlkKSB7XG4gICAgICBmZXRjaEFuYWx5dGljc0RhdGEoKTtcbiAgICB9XG4gIH0sIFtzdWJzY3JpcHRpb24/LnVzZXI/LmlkLCB0aW1lUmFuZ2UsIHNlbGVjdGVkQ29uZmlnXSk7XG5cbiAgY29uc3QgZmV0Y2hDdXN0b21Db25maWdzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2N1c3RvbS1jb25maWdzJyk7XG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgY29uZmlncyA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgc2V0Q3VzdG9tQ29uZmlncyhjb25maWdzKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGNvbmZpZ3M6JywgZXJyKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZmV0Y2hBbmFseXRpY3NEYXRhID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCdbQW5hbHl0aWNzXSBTdGFydGluZyBkYXRhIGZldGNoIGZvciB1c2VyOicsIHN1YnNjcmlwdGlvbj8udXNlcj8uaWQpO1xuXG4gICAgICAvLyBCdWlsZCBxdWVyeSBwYXJhbWV0ZXJzXG4gICAgICBjb25zdCBwYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgICBjb25zdCBzdGFydERhdGUgPSBuZXcgRGF0ZSgpO1xuICAgICAgc3RhcnREYXRlLnNldERhdGUoc3RhcnREYXRlLmdldERhdGUoKSAtIHBhcnNlSW50KHRpbWVSYW5nZSkpO1xuICAgICAgcGFyYW1zLmFwcGVuZCgnc3RhcnREYXRlJywgc3RhcnREYXRlLnRvSVNPU3RyaW5nKCkpO1xuXG4gICAgICBpZiAoc2VsZWN0ZWRDb25maWcpIHtcbiAgICAgICAgcGFyYW1zLmFwcGVuZCgnY3VzdG9tQXBpQ29uZmlnSWQnLCBzZWxlY3RlZENvbmZpZyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCdbQW5hbHl0aWNzXSBGZXRjaGluZyB3aXRoIHBhcmFtczonLCBwYXJhbXMudG9TdHJpbmcoKSk7XG5cbiAgICAgIC8vIEJ1aWxkIHByZXZpb3VzIHBlcmlvZCBwYXJhbXMgZm9yIGNvbXBhcmlzb25cbiAgICAgIGNvbnN0IHByZXZQYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgICBjb25zdCBwcmV2U3RhcnREYXRlID0gbmV3IERhdGUoKTtcbiAgICAgIHByZXZTdGFydERhdGUuc2V0RGF0ZShwcmV2U3RhcnREYXRlLmdldERhdGUoKSAtIHBhcnNlSW50KHRpbWVSYW5nZSkgKiAyKTtcbiAgICAgIGNvbnN0IHByZXZFbmREYXRlID0gbmV3IERhdGUoKTtcbiAgICAgIHByZXZFbmREYXRlLnNldERhdGUocHJldkVuZERhdGUuZ2V0RGF0ZSgpIC0gcGFyc2VJbnQodGltZVJhbmdlKSk7XG4gICAgICBwcmV2UGFyYW1zLmFwcGVuZCgnc3RhcnREYXRlJywgcHJldlN0YXJ0RGF0ZS50b0lTT1N0cmluZygpKTtcbiAgICAgIHByZXZQYXJhbXMuYXBwZW5kKCdlbmREYXRlJywgcHJldkVuZERhdGUudG9JU09TdHJpbmcoKSk7XG5cbiAgICAgIGlmIChzZWxlY3RlZENvbmZpZykge1xuICAgICAgICBwcmV2UGFyYW1zLmFwcGVuZCgnY3VzdG9tQXBpQ29uZmlnSWQnLCBzZWxlY3RlZENvbmZpZyk7XG4gICAgICB9XG5cbiAgICAgIC8vIEZldGNoIGFuYWx5dGljcyBkYXRhXG4gICAgICBjb25zdCBbcmVzcG9uc2UsIHByZXZSZXNwb25zZSwgdGltZVNlcmllc1Jlc3BvbnNlXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgICAgZmV0Y2goYC9hcGkvYW5hbHl0aWNzL3N1bW1hcnk/JHtwYXJhbXMudG9TdHJpbmcoKX0mZ3JvdXBCeT1wcm92aWRlcmApLFxuICAgICAgICBmZXRjaChgL2FwaS9hbmFseXRpY3Mvc3VtbWFyeT8ke3ByZXZQYXJhbXMudG9TdHJpbmcoKX0mZ3JvdXBCeT1wcm92aWRlcmApLFxuICAgICAgICBmZXRjaChgL2FwaS9hbmFseXRpY3Mvc3VtbWFyeT8ke3BhcmFtcy50b1N0cmluZygpfSZncm91cEJ5PWRheWApXG4gICAgICBdKTtcblxuICAgICAgY29uc29sZS5sb2coJ1tBbmFseXRpY3NdIFJlc3BvbnNlIHN0YXR1czonLCByZXNwb25zZS5zdGF0dXMpO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcbiAgICAgICAgY29uc29sZS5lcnJvcignW0FuYWx5dGljc10gQVBJIEVycm9yOicsIGVycm9yVGV4dCk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIGFuYWx5dGljcyBkYXRhOiAke3Jlc3BvbnNlLnN0YXR1c30gJHtlcnJvclRleHR9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBjb25zdCBwcmV2RGF0YSA9IHByZXZSZXNwb25zZS5vayA/IGF3YWl0IHByZXZSZXNwb25zZS5qc29uKCkgOiBudWxsO1xuICAgICAgY29uc3QgdGltZVNlcmllc0RhdGEgPSB0aW1lU2VyaWVzUmVzcG9uc2Uub2sgPyBhd2FpdCB0aW1lU2VyaWVzUmVzcG9uc2UuanNvbigpIDogbnVsbDtcblxuICAgICAgY29uc29sZS5sb2coJ1tBbmFseXRpY3NdIFJlY2VpdmVkIGRhdGE6Jywge1xuICAgICAgICBzdW1tYXJ5OiBkYXRhLnN1bW1hcnksXG4gICAgICAgIGdyb3VwZWREYXRhQ291bnQ6IGRhdGEuZ3JvdXBlZF9kYXRhPy5sZW5ndGggfHwgMCxcbiAgICAgICAgdGltZVNlcmllc0NvdW50OiB0aW1lU2VyaWVzRGF0YT8uZ3JvdXBlZF9kYXRhPy5sZW5ndGggfHwgMFxuICAgICAgfSk7XG5cbiAgICAgIHNldEFuYWx5dGljc0RhdGEoZGF0YSk7XG4gICAgICBzZXRQcmV2aW91c1BlcmlvZERhdGEocHJldkRhdGEpO1xuXG4gICAgICBpZiAodGltZVNlcmllc0RhdGE/Lmdyb3VwZWRfZGF0YSkge1xuICAgICAgICBjb25zdCBmb3JtYXR0ZWRUaW1lU2VyaWVzID0gdGltZVNlcmllc0RhdGEuZ3JvdXBlZF9kYXRhLm1hcCgoaXRlbTogYW55KSA9PiAoe1xuICAgICAgICAgIGRhdGU6IGl0ZW0ucGVyaW9kIHx8IGl0ZW0ubmFtZSxcbiAgICAgICAgICBjb3N0OiBpdGVtLmNvc3QgfHwgMCxcbiAgICAgICAgICByZXF1ZXN0czogaXRlbS5yZXF1ZXN0cyB8fCAwLFxuICAgICAgICAgIHRva2VuczogKGl0ZW0uaW5wdXRfdG9rZW5zIHx8IDApICsgKGl0ZW0ub3V0cHV0X3Rva2VucyB8fCAwKSxcbiAgICAgICAgICBsYXRlbmN5OiBpdGVtLmF2Z19sYXRlbmN5IHx8IDBcbiAgICAgICAgfSkpO1xuICAgICAgICBzZXRUaW1lU2VyaWVzRGF0YShmb3JtYXR0ZWRUaW1lU2VyaWVzKTtcbiAgICAgICAgY29uc29sZS5sb2coJ1tBbmFseXRpY3NdIEZvcm1hdHRlZCB0aW1lIHNlcmllczonLCBmb3JtYXR0ZWRUaW1lU2VyaWVzLmxlbmd0aCwgJ2l0ZW1zJyk7XG4gICAgICB9XG5cbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignW0FuYWx5dGljc10gRXJyb3IgZmV0Y2hpbmcgZGF0YTonLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoZXJyLm1lc3NhZ2UpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtzdWJzY3JpcHRpb24/LnVzZXI/LmlkLCB0aW1lUmFuZ2UsIHNlbGVjdGVkQ29uZmlnXSk7XG5cbiAgLy8gTG9hZGluZyBzdGF0ZVxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1bIzA0MDcxNl0gdGV4dC13aGl0ZVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTYgcHktOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1wdWxzZSBzcGFjZS15LThcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC04IGJnLWdyYXktODAwIHJvdW5kZWQgdy0xLzNcIj48L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgICAgICAgICB7Wy4uLkFycmF5KDQpXS5tYXAoKF8sIGkpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17aX0gY2xhc3NOYW1lPVwiYmctZ3JheS05MDAgcm91bmRlZC1sZyBwLTYgYm9yZGVyIGJvcmRlci1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctZ3JheS04MDAgcm91bmRlZCB3LTEvMiBtYi00XCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCBiZy1ncmF5LTgwMCByb3VuZGVkIHctMy80IG1iLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0zIGJnLWdyYXktODAwIHJvdW5kZWQgdy0xLzNcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgLy8gRXJyb3Igc3RhdGVcbiAgaWYgKGVycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLVsjMDQwNzE2XSB0ZXh0LXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNiBweS04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbWItNFwiPkFuYWx5dGljczwvaDE+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC05MDAvMjAgYm9yZGVyIGJvcmRlci1yZWQtODAwIHJvdW5kZWQtbGcgcC02XCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTQwMCBtYi00XCI+RXJyb3IgbG9hZGluZyBhbmFseXRpY3M6IHtlcnJvcn08L3A+XG4gICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgb25DbGljaz17ZmV0Y2hBbmFseXRpY3NEYXRhfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1jeWFuLTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQgaG92ZXI6YmctY3lhbi02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgUmV0cnlcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgY29uc3Qgc3VtbWFyeSA9IGFuYWx5dGljc0RhdGE/LnN1bW1hcnk7XG4gIGNvbnN0IHByZXZpb3VzU3VtbWFyeSA9IHByZXZpb3VzUGVyaW9kRGF0YT8uc3VtbWFyeTtcblxuICAvLyBDYWxjdWxhdGUgdHJlbmRzXG4gIGNvbnN0IGNvc3RUcmVuZCA9IHByZXZpb3VzU3VtbWFyeSA/IGNhbGN1bGF0ZVRyZW5kKHN1bW1hcnk/LnRvdGFsX2Nvc3QgfHwgMCwgcHJldmlvdXNTdW1tYXJ5LnRvdGFsX2Nvc3QpIDogbnVsbDtcbiAgY29uc3QgcmVxdWVzdFRyZW5kID0gcHJldmlvdXNTdW1tYXJ5ID8gY2FsY3VsYXRlVHJlbmQoc3VtbWFyeT8udG90YWxfcmVxdWVzdHMgfHwgMCwgcHJldmlvdXNTdW1tYXJ5LnRvdGFsX3JlcXVlc3RzKSA6IG51bGw7XG4gIGNvbnN0IGxhdGVuY3lUcmVuZCA9IHByZXZpb3VzU3VtbWFyeSA/IGNhbGN1bGF0ZVRyZW5kKHN1bW1hcnk/LmF2ZXJhZ2VfbGF0ZW5jeSB8fCAwLCBwcmV2aW91c1N1bW1hcnkuYXZlcmFnZV9sYXRlbmN5IHx8IDApIDogbnVsbDtcbiAgY29uc3Qgc3VjY2Vzc1RyZW5kID0gcHJldmlvdXNTdW1tYXJ5ID8gY2FsY3VsYXRlVHJlbmQoc3VtbWFyeT8uc3VjY2Vzc19yYXRlIHx8IDAsIHByZXZpb3VzU3VtbWFyeS5zdWNjZXNzX3JhdGUpIDogbnVsbDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLVsjMDQwNzE2XSB0ZXh0LXdoaXRlXCI+XG4gICAgICB7LyogSGVhZGVyIFNlY3Rpb24gLSBGb2xsb3dpbmcgcmVmZXJlbmNlIGRlc2lnbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWdyYXktODAwLzUwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNiBweS02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC04XCI+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5BbmFseXRpY3M8L2gxPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicHgtMyBweS0xIHRleHQtc20gdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICBXb3Jrc3BhY2VcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInB4LTMgcHktMSB0ZXh0LXNtIGJnLWN5YW4tNTAwIHRleHQtd2hpdGUgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgT3JnYW5pc2F0aW9uXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiB0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICA8TWFnbmlmeWluZ0dsYXNzSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIEZpbHRlclwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy10cmFuc3BhcmVudCBib3JkZXItbm9uZSBvdXRsaW5lLW5vbmUgdGV4dC1ncmF5LTQwMCBwbGFjZWhvbGRlci1ncmF5LTUwMCB3LTMyXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgIHZhbHVlPXt0aW1lUmFuZ2V9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRUaW1lUmFuZ2UoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBiZy1ncmF5LTgwMCBib3JkZXIgYm9yZGVyLWdyYXktNzAwIHJvdW5kZWQgdGV4dC1zbSB0ZXh0LXdoaXRlIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpib3JkZXItY3lhbi01MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjdcIj5MYXN0IDcgZGF5czwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCIzMFwiPkxhc3QgMzAgZGF5czwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCI5MFwiPkxhc3QgOTAgZGF5czwvb3B0aW9uPlxuICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTmF2aWdhdGlvbiBUYWJzIC0gRm9sbG93aW5nIHJlZmVyZW5jZSBkZXNpZ24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ncmF5LTgwMC81MFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtOFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ292ZXJ2aWV3Jyl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweS00IGJvcmRlci1iLTIgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdvdmVydmlldydcbiAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1jeWFuLTUwMCB0ZXh0LWN5YW4tNTAwJ1xuICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxFeWVJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+T3ZlcnZpZXc8L3NwYW4+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCd1c2VycycpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHktNCBib3JkZXItYi0yIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAndXNlcnMnXG4gICAgICAgICAgICAgICAgICA/ICdib3JkZXItY3lhbi01MDAgdGV4dC1jeWFuLTUwMCdcbiAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8VXNlcnNJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+VXNlcnM8L3NwYW4+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdlcnJvcnMnKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHB5LTQgYm9yZGVyLWItMiB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2Vycm9ycydcbiAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1jeWFuLTUwMCB0ZXh0LWN5YW4tNTAwJ1xuICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxFeGNsYW1hdGlvblRyaWFuZ2xlSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPkVycm9yczwvc3Bhbj5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ2NhY2hlJyl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweS00IGJvcmRlci1iLTIgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdjYWNoZSdcbiAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1jeWFuLTUwMCB0ZXh0LWN5YW4tNTAwJ1xuICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxTZXJ2ZXJJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+Q2FjaGU8L3NwYW4+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdmZWVkYmFjaycpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHktNCBib3JkZXItYi0yIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnZmVlZGJhY2snXG4gICAgICAgICAgICAgICAgICA/ICdib3JkZXItY3lhbi01MDAgdGV4dC1jeWFuLTUwMCdcbiAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8Q2hhdEJ1YmJsZUxlZnRSaWdodEljb24gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5GZWVkYmFjazwvc3Bhbj5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ21ldGFkYXRhJyl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweS00IGJvcmRlci1iLTIgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdtZXRhZGF0YSdcbiAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1jeWFuLTUwMCB0ZXh0LWN5YW4tNTAwJ1xuICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxEb2N1bWVudFRleHRJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+TWV0YWRhdGE8L3NwYW4+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1haW4gQ29udGVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNiBweS04XCI+XG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdvdmVydmlldycgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICAgICAgICB7LyogTWFpbiBNZXRyaWNzIEdyaWQgLSBGb2xsb3dpbmcgcmVmZXJlbmNlIGRlc2lnbiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgICAgICAgICB7LyogVG90YWwgUmVxdWVzdHMgTWFkZSAqL31cbiAgICAgICAgICAgICAgPE1ldHJpY0NhcmRcbiAgICAgICAgICAgICAgICB0aXRsZT1cIlRvdGFsIFJlcXVlc3QgTWFkZVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e3N1bW1hcnk/LnRvdGFsX3JlcXVlc3RzIHx8IDB9XG4gICAgICAgICAgICAgICAgdHJlbmQ9e3JlcXVlc3RUcmVuZH1cbiAgICAgICAgICAgICAgICBpY29uPXs8Q2hhcnRCYXJJY29uIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPn1cbiAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICB7LyogQXZlcmFnZSBMYXRlbmN5ICovfVxuICAgICAgICAgICAgICA8TWV0cmljQ2FyZFxuICAgICAgICAgICAgICAgIHRpdGxlPVwiQXZlcmFnZSBMYXRlbmN5XCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17YCR7TWF0aC5yb3VuZChzdW1tYXJ5Py5hdmVyYWdlX2xhdGVuY3kgfHwgMCl9bXNgfVxuICAgICAgICAgICAgICAgIHRyZW5kPXtsYXRlbmN5VHJlbmR9XG4gICAgICAgICAgICAgICAgaWNvbj17PENsb2NrSWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz59XG4gICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgey8qIFVzZXIgRmVlZGJhY2sgKi99XG4gICAgICAgICAgICAgIDxNZXRyaWNDYXJkXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJVc2VyIEZlZWRiYWNrXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17YCR7KHN1bW1hcnk/LnN1Y2Nlc3NfcmF0ZSB8fCAwKS50b0ZpeGVkKDEpfSVgfVxuICAgICAgICAgICAgICAgIHRyZW5kPXtzdWNjZXNzVHJlbmR9XG4gICAgICAgICAgICAgICAgaWNvbj17PENoYXRCdWJibGVMZWZ0UmlnaHRJY29uIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPn1cbiAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICB7LyogVG90YWwgQ29zdCAqL31cbiAgICAgICAgICAgICAgPE1ldHJpY0NhcmRcbiAgICAgICAgICAgICAgICB0aXRsZT1cIlRvdGFsIENvc3RcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtYXRDdXJyZW5jeShzdW1tYXJ5Py50b3RhbF9jb3N0IHx8IDApfVxuICAgICAgICAgICAgICAgIHRyZW5kPXtjb3N0VHJlbmR9XG4gICAgICAgICAgICAgICAgaWNvbj17PEN1cnJlbmN5RG9sbGFySWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz59XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFNob3cgZW1wdHkgc3RhdGUgaWYgbm8gZGF0YSAqL31cbiAgICAgICAgICAgIHsoIXN1bW1hcnkgfHwgc3VtbWFyeS50b3RhbF9yZXF1ZXN0cyA9PT0gMCkgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgICAgPENoYXJ0QmFySWNvbiBjbGFzc05hbWU9XCJoLTE2IHctMTYgbXgtYXV0byBtYi00IHRleHQtZ3JheS01MDBcIiAvPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+Tm8gQW5hbHl0aWNzIERhdGEgWWV0PC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIFN0YXJ0IG1ha2luZyBBUEkgcmVxdWVzdHMgdG8gc2VlIHlvdXIgYW5hbHl0aWNzIGRhdGEgaGVyZS5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICBBbmFseXRpY3Mgd2lsbCBhcHBlYXIgb25jZSB5b3UgYmVnaW4gdXNpbmcgeW91ciBBUEkgY29uZmlndXJhdGlvbnMuXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBDaGFydHMgU2VjdGlvbiAtIEZvbGxvd2luZyByZWZlcmVuY2UgbGF5b3V0ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgIHsvKiBDb3N0IENoYXJ0ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwLzUwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC1sZyBwLTYgYm9yZGVyIGJvcmRlci1ncmF5LTgwMC81MFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZVwiPkNvc3Q8L2gzPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtdC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRDdXJyZW5jeShzdW1tYXJ5Py50b3RhbF9jb3N0IHx8IDApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICB7Y29zdFRyZW5kICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQtc20gcHgtMiBweS0xIHJvdW5kZWQgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvc3RUcmVuZC5pc1Bvc2l0aXZlID8gJ3RleHQtcmVkLTQwMCBiZy1yZWQtNDAwLzEwJyA6ICd0ZXh0LWdyZWVuLTQwMCBiZy1ncmVlbi00MDAvMTAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3N0VHJlbmQuaXNQb3NpdGl2ZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXJyb3dUcmVuZGluZ1VwSWNvbiBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXJyb3dUcmVuZGluZ0Rvd25JY29uIGNsYXNzTmFtZT1cInctMyBoLTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57Y29zdFRyZW5kLnBlcmNlbnRhZ2UudG9GaXhlZCgxKX0lPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPEN1cnJlbmN5RG9sbGFySWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIENvc3QgQ2hhcnQgVmlzdWFsaXphdGlvbiAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNDggcmVsYXRpdmUgYmctZ3JheS04MDAvNTAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAge3RpbWVTZXJpZXNEYXRhLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbFwiIHZpZXdCb3g9XCIwIDAgNDAwIDEyMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRlZnM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD1cImNvc3RHcmFkaWVudFwiIHgxPVwiMCVcIiB5MT1cIjAlXCIgeDI9XCIwJVwiIHkyPVwiMTAwJVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdG9wIG9mZnNldD1cIjAlXCIgc3RvcENvbG9yPVwiIzEwYjk4MVwiIHN0b3BPcGFjaXR5PVwiMC4zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3RvcCBvZmZzZXQ9XCIxMDAlXCIgc3RvcENvbG9yPVwiIzEwYjk4MVwiIHN0b3BPcGFjaXR5PVwiMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGluZWFyR3JhZGllbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2RlZnM+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBHcmlkIGxpbmVzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAge1suLi5BcnJheSg1KV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxsaW5lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHgxPVwiMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgeTE9e2kgKiAyNH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB4Mj1cIjQwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgeTI9e2kgKiAyNH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCIjMzc0MTUxXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjAuNVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eT1cIjAuM1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApKX1cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIENvc3QgbGluZSAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwb2x5bGluZVxuICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZT1cIiMxMGI5ODFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBwb2ludHM9e3RpbWVTZXJpZXNEYXRhLm1hcCgoZCwgaSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHggPSAoaSAvIE1hdGgubWF4KHRpbWVTZXJpZXNEYXRhLmxlbmd0aCAtIDEsIDEpKSAqIDQwMDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtYXhDb3N0ID0gTWF0aC5tYXgoLi4udGltZVNlcmllc0RhdGEubWFwKGl0ZW0gPT4gaXRlbS5jb3N0KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgeSA9IDEyMCAtIChkLmNvc3QgLyBtYXhDb3N0KSAqIDEwMDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gYCR7eH0sJHt5fWA7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0pLmpvaW4oJyAnKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBBcmVhIGZpbGwgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8cG9seWdvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwidXJsKCNjb3N0R3JhZGllbnQpXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcG9pbnRzPXtgMCwxMjAgJHt0aW1lU2VyaWVzRGF0YS5tYXAoKGQsIGkpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB4ID0gKGkgLyBNYXRoLm1heCh0aW1lU2VyaWVzRGF0YS5sZW5ndGggLSAxLCAxKSkgKiA0MDA7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbWF4Q29zdCA9IE1hdGgubWF4KC4uLnRpbWVTZXJpZXNEYXRhLm1hcChpdGVtID0+IGl0ZW0uY29zdCkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHkgPSAxMjAgLSAoZC5jb3N0IC8gbWF4Q29zdCkgKiAxMDA7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGAke3h9LCR7eX1gO1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9KS5qb2luKCcgJyl9IDQwMCwxMjBgfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGFydEJhckljb24gY2xhc3NOYW1lPVwiaC04IHctOCBteC1hdXRvIG1iLTIgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5ObyBkYXRhIGF2YWlsYWJsZTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogTGF0ZW5jeSBDaGFydCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMC81MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtbGcgcC02IGJvcmRlciBib3JkZXItZ3JheS04MDAvNTBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5MYXRlbmN5PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7TWF0aC5yb3VuZChzdW1tYXJ5Py5hdmVyYWdlX2xhdGVuY3kgfHwgMCl9bXNcbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAge2xhdGVuY3lUcmVuZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXNtIHB4LTIgcHktMSByb3VuZGVkIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBsYXRlbmN5VHJlbmQuaXNQb3NpdGl2ZSA/ICd0ZXh0LXJlZC00MDAgYmctcmVkLTQwMC8xMCcgOiAndGV4dC1ncmVlbi00MDAgYmctZ3JlZW4tNDAwLzEwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bGF0ZW5jeVRyZW5kLmlzUG9zaXRpdmUgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFycm93VHJlbmRpbmdVcEljb24gY2xhc3NOYW1lPVwidy0zIGgtM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFycm93VHJlbmRpbmdEb3duSWNvbiBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e2xhdGVuY3lUcmVuZC5wZXJjZW50YWdlLnRvRml4ZWQoMSl9JTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxDbG9ja0ljb24gY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBMYXRlbmN5IENoYXJ0IFZpc3VhbGl6YXRpb24gKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQ4IHJlbGF0aXZlIGJnLWdyYXktODAwLzUwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgIHt0aW1lU2VyaWVzRGF0YS5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGxcIiB2aWV3Qm94PVwiMCAwIDQwMCAxMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkZWZzPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8bGluZWFyR3JhZGllbnQgaWQ9XCJsYXRlbmN5R3JhZGllbnRcIiB4MT1cIjAlXCIgeTE9XCIwJVwiIHgyPVwiMCVcIiB5Mj1cIjEwMCVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3RvcCBvZmZzZXQ9XCIwJVwiIHN0b3BDb2xvcj1cIiNmNTllMGJcIiBzdG9wT3BhY2l0eT1cIjAuM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN0b3Agb2Zmc2V0PVwiMTAwJVwiIHN0b3BDb2xvcj1cIiNmNTllMGJcIiBzdG9wT3BhY2l0eT1cIjBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpbmVhckdyYWRpZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kZWZzPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogR3JpZCBsaW5lcyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIHtbLi4uQXJyYXkoNSldLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8bGluZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB4MT1cIjBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHkxPXtpICogMjR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgeDI9XCI0MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHkyPXtpICogMjR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiIzM3NDE1MVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIwLjVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wYWNpdHk9XCIwLjNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBMYXRlbmN5IGxpbmUgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8cG9seWxpbmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCIjZjU5ZTBiXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcG9pbnRzPXt0aW1lU2VyaWVzRGF0YS5tYXAoKGQsIGkpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB4ID0gKGkgLyBNYXRoLm1heCh0aW1lU2VyaWVzRGF0YS5sZW5ndGggLSAxLCAxKSkgKiA0MDA7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbWF4TGF0ZW5jeSA9IE1hdGgubWF4KC4uLnRpbWVTZXJpZXNEYXRhLm1hcChpdGVtID0+IGl0ZW0ubGF0ZW5jeSB8fCAwKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgeSA9IDEyMCAtICgoZC5sYXRlbmN5IHx8IDApIC8gTWF0aC5tYXgobWF4TGF0ZW5jeSwgMSkpICogMTAwO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBgJHt4fSwke3l9YDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSkuam9pbignICcpfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIEFyZWEgZmlsbCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwb2x5Z29uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJ1cmwoI2xhdGVuY3lHcmFkaWVudClcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBwb2ludHM9e2AwLDEyMCAke3RpbWVTZXJpZXNEYXRhLm1hcCgoZCwgaSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHggPSAoaSAvIE1hdGgubWF4KHRpbWVTZXJpZXNEYXRhLmxlbmd0aCAtIDEsIDEpKSAqIDQwMDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtYXhMYXRlbmN5ID0gTWF0aC5tYXgoLi4udGltZVNlcmllc0RhdGEubWFwKGl0ZW0gPT4gaXRlbS5sYXRlbmN5IHx8IDApKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB5ID0gMTIwIC0gKChkLmxhdGVuY3kgfHwgMCkgLyBNYXRoLm1heChtYXhMYXRlbmN5LCAxKSkgKiAxMDA7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGAke3h9LCR7eX1gO1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9KS5qb2luKCcgJyl9IDQwMCwxMjBgfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDbG9ja0ljb24gY2xhc3NOYW1lPVwiaC04IHctOCBteC1hdXRvIG1iLTIgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5ObyBkYXRhIGF2YWlsYWJsZTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQm90dG9tIFNlY3Rpb24gLSBUb2tlbiBVc2FnZSBhbmQgUHJvdmlkZXIgQnJlYWtkb3duICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgICAgIHsvKiBUb2tlbnMgVXNlZCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMC81MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtbGcgcC02IGJvcmRlciBib3JkZXItZ3JheS04MDAvNTBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5Ub2tlbnMgVXNlZDwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMCBtdC0xXCI+TWFyY2ggMjg8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8Q3B1Q2hpcEljb24gY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi02XCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TnVtYmVyKHN1bW1hcnk/LnRvdGFsX3Rva2VucyB8fCAwKX1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmVlbi00MDAgYmctZ3JlZW4tNDAwLzEwIHB4LTIgcHktMSByb3VuZGVkIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8QXJyb3dUcmVuZGluZ1VwSWNvbiBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+OC4zOSU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogVG9rZW4gU2NhdHRlciBQbG90ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0zMiByZWxhdGl2ZSBiZy1ncmF5LTgwMC81MCByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgey8qIExlZ2VuZCAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgbWItNCB0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLXllbGxvdy01MDAgcm91bmRlZC1mdWxsIG1yLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5JbnB1dCBUb2tlbjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctZ3JlZW4tNTAwIHJvdW5kZWQtZnVsbCBtci0yXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+T3V0cHV0IFRva2VuPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ibHVlLTUwMCByb3VuZGVkLWZ1bGwgbXItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPlRvdGFsIFRva2VuPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogU2NhdHRlciBkb3RzICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGgtMTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Wy4uLkFycmF5KDIwKV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYWJzb2x1dGUgdy0xIGgtMSByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpICUgMyA9PT0gMCA/ICdiZy15ZWxsb3ctNTAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaSAlIDMgPT09IDEgPyAnYmctZ3JlZW4tNTAwJyA6ICdiZy1ibHVlLTUwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGVmdDogYCR7TWF0aC5yYW5kb20oKSAqIDkwfSVgLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRvcDogYCR7TWF0aC5yYW5kb20oKSAqIDgwfSVgLFxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFJlcXVlc3RzICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwLzUwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC1sZyBwLTYgYm9yZGVyIGJvcmRlci1ncmF5LTgwMC81MFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZVwiPlJlcXVlc3RzPC9oMz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxHbG9iZUFsdEljb24gY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi02XCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TnVtYmVyKHN1bW1hcnk/LnRvdGFsX3JlcXVlc3RzIHx8IDApfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyZWVuLTQwMCBiZy1ncmVlbi00MDAvMTAgcHgtMiBweS0xIHJvdW5kZWQgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxBcnJvd1RyZW5kaW5nVXBJY29uIGNsYXNzTmFtZT1cInctMyBoLTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj4zLjM5JTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBIb3Jpem9udGFsIEJhciBDaGFydCAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgICAge2FuYWx5dGljc0RhdGE/Lmdyb3VwZWRfZGF0YS5zbGljZSgwLCA1KS5tYXAoKHByb3ZpZGVyLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17cHJvdmlkZXIubmFtZX0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgdGV4dC1zbSB0ZXh0LWdyYXktNDAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cHJvdmlkZXIubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMiBiZy1ncmF5LTgwMCByb3VuZGVkLWZ1bGwgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BoLWZ1bGwgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleCA9PT0gMCA/ICdiZy1waW5rLTUwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXggPT09IDEgPyAnYmctcHVycGxlLTUwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXggPT09IDIgPyAnYmctY3lhbi01MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4ID09PSAzID8gJ2JnLWdyZWVuLTUwMCcgOiAnYmcteWVsbG93LTUwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IGAkeyhwcm92aWRlci5yZXF1ZXN0cyAvIChzdW1tYXJ5Py50b3RhbF9yZXF1ZXN0cyB8fCAxKSkgKiAxMDB9JWBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDAgdy0xMiB0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TnVtYmVyKHByb3ZpZGVyLnJlcXVlc3RzKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFVuaXF1ZSBVc2VycyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMC81MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtbGcgcC02IGJvcmRlciBib3JkZXItZ3JheS04MDAvNTBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5VbmlxdWUgVXNlcnM8L2gzPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPFVzZXJzSWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgIHtzdW1tYXJ5Py5zdWNjZXNzZnVsX3JlcXVlc3RzIHx8IDB9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tNDAwIGJnLWdyZWVuLTQwMC8xMCBweC0yIHB5LTEgcm91bmRlZCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPEFycm93VHJlbmRpbmdVcEljb24gY2xhc3NOYW1lPVwidy0zIGgtM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPjMuMzklPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIFdhdmUgQ2hhcnQgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTMyIHJlbGF0aXZlIGJnLWdyYXktODAwLzUwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGxcIiB2aWV3Qm94PVwiMCAwIDIwMCA4MFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkZWZzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpbmVhckdyYWRpZW50IGlkPVwid2F2ZUdyYWRpZW50XCIgeDE9XCIwJVwiIHkxPVwiMCVcIiB4Mj1cIjAlXCIgeTI9XCIxMDAlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzdG9wIG9mZnNldD1cIjAlXCIgc3RvcENvbG9yPVwiIzhiNWNmNlwiIHN0b3BPcGFjaXR5PVwiMC4zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHN0b3Agb2Zmc2V0PVwiMTAwJVwiIHN0b3BDb2xvcj1cIiM4YjVjZjZcIiBzdG9wT3BhY2l0eT1cIjBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9saW5lYXJHcmFkaWVudD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2RlZnM+XG5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogV2F2ZSBwYXRoICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTAsNDAgUTUwLDIwIDEwMCw0MCBUMjAwLDQwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZT1cIiM4YjVjZjZcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIyXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIFdhdmUgYXJlYSAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk0wLDQwIFE1MCwyMCAxMDAsNDAgVDIwMCw0MCBMMjAwLDgwIEwwLDgwIFpcIlxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cInVybCgjd2F2ZUdyYWRpZW50KVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogT3RoZXIgdGFicyBjb250ZW50ICovfVxuICAgICAgICB7YWN0aXZlVGFiID09PSAndXNlcnMnICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICA8VXNlcnNJY29uIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItMlwiPlVzZXJzIEFuYWx5dGljczwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+VXNlciBhbmFseXRpY3MgY29taW5nIHNvb24uLi48L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2Vycm9ycycgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICAgIDxFeGNsYW1hdGlvblRyaWFuZ2xlSWNvbiBjbGFzc05hbWU9XCJoLTEyIHctMTIgbXgtYXV0byBtYi00IHRleHQtZ3JheS01MDBcIiAvPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5FcnJvciBBbmFseXRpY3M8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPkVycm9yIGFuYWx5dGljcyBjb21pbmcgc29vbi4uLjwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAnY2FjaGUnICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICA8U2VydmVySWNvbiBjbGFzc05hbWU9XCJoLTEyIHctMTIgbXgtYXV0byBtYi00IHRleHQtZ3JheS01MDBcIiAvPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5DYWNoZSBBbmFseXRpY3M8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPkNhY2hlIGFuYWx5dGljcyBjb21pbmcgc29vbi4uLjwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAnZmVlZGJhY2snICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICA8Q2hhdEJ1YmJsZUxlZnRSaWdodEljb24gY2xhc3NOYW1lPVwiaC0xMiB3LTEyIG14LWF1dG8gbWItNCB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+RmVlZGJhY2sgQW5hbHl0aWNzPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5GZWVkYmFjayBhbmFseXRpY3MgY29taW5nIHNvb24uLi48L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ21ldGFkYXRhJyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgPERvY3VtZW50VGV4dEljb24gY2xhc3NOYW1lPVwiaC0xMiB3LTEyIG14LWF1dG8gbWItNCB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+TWV0YWRhdGEgQW5hbHl0aWNzPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5NZXRhZGF0YSBhbmFseXRpY3MgY29taW5nIHNvb24uLi48L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQW5hbHl0aWNzUGFnZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8U3VzcGVuc2UgZmFsbGJhY2s9e1xuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctWyMwNDA3MTZdIHRleHQtd2hpdGVcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC02IHB5LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtcHVsc2Ugc3BhY2UteS04XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCBiZy1ncmF5LTgwMCByb3VuZGVkIHctMS8zXCI+PC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTZcIj5cbiAgICAgICAgICAgICAge1suLi5BcnJheSg0KV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2l9IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIHJvdW5kZWQtbGcgcC02IGJvcmRlciBib3JkZXItZ3JheS04MDBcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktODAwIHJvdW5kZWQgdy0xLzIgbWItNFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTggYmctZ3JheS04MDAgcm91bmRlZCB3LTMvNCBtYi0yXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1ncmF5LTgwMCByb3VuZGVkIHctMS8zXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgfT5cbiAgICAgIDxBbmFseXRpY3NQYWdlQ29udGVudCAvPlxuICAgIDwvU3VzcGVuc2U+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIlN1c3BlbnNlIiwidXNlUm91dGVyIiwidXNlU3Vic2NyaXB0aW9uIiwiTWFnbmlmeWluZ0dsYXNzSWNvbiIsIkNoYXJ0QmFySWNvbiIsIlVzZXJzSWNvbiIsIkV4Y2xhbWF0aW9uVHJpYW5nbGVJY29uIiwiU2VydmVySWNvbiIsIkNoYXRCdWJibGVMZWZ0UmlnaHRJY29uIiwiRG9jdW1lbnRUZXh0SWNvbiIsIkV5ZUljb24iLCJBcnJvd1RyZW5kaW5nVXBJY29uIiwiQXJyb3dUcmVuZGluZ0Rvd25JY29uIiwiQ2xvY2tJY29uIiwiQ3VycmVuY3lEb2xsYXJJY29uIiwiQ3B1Q2hpcEljb24iLCJHbG9iZUFsdEljb24iLCJmb3JtYXROdW1iZXIiLCJudW0iLCJ0b0ZpeGVkIiwidG9TdHJpbmciLCJmb3JtYXRDdXJyZW5jeSIsImFtb3VudCIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImN1cnJlbmN5IiwibWluaW11bUZyYWN0aW9uRGlnaXRzIiwibWF4aW11bUZyYWN0aW9uRGlnaXRzIiwiZm9ybWF0IiwiY2FsY3VsYXRlVHJlbmQiLCJjdXJyZW50IiwicHJldmlvdXMiLCJwZXJjZW50YWdlIiwiaXNQb3NpdGl2ZSIsIk1hdGgiLCJhYnMiLCJNZXRyaWNDYXJkIiwidGl0bGUiLCJ2YWx1ZSIsInRyZW5kIiwiaWNvbiIsInN1YnRpdGxlIiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsInNwYW4iLCJBbmFseXRpY3NQYWdlQ29udGVudCIsInN1YnNjcmlwdGlvbiIsInJvdXRlciIsImFuYWx5dGljc0RhdGEiLCJzZXRBbmFseXRpY3NEYXRhIiwicHJldmlvdXNQZXJpb2REYXRhIiwic2V0UHJldmlvdXNQZXJpb2REYXRhIiwidGltZVNlcmllc0RhdGEiLCJzZXRUaW1lU2VyaWVzRGF0YSIsImN1c3RvbUNvbmZpZ3MiLCJzZXRDdXN0b21Db25maWdzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwidGltZVJhbmdlIiwic2V0VGltZVJhbmdlIiwic2VsZWN0ZWRDb25maWciLCJzZXRTZWxlY3RlZENvbmZpZyIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsImZldGNoQ3VzdG9tQ29uZmlncyIsInVzZXIiLCJpZCIsImZldGNoQW5hbHl0aWNzRGF0YSIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsImNvbmZpZ3MiLCJqc29uIiwiZXJyIiwiY29uc29sZSIsImRhdGEiLCJsb2ciLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJzdGFydERhdGUiLCJEYXRlIiwic2V0RGF0ZSIsImdldERhdGUiLCJwYXJzZUludCIsImFwcGVuZCIsInRvSVNPU3RyaW5nIiwicHJldlBhcmFtcyIsInByZXZTdGFydERhdGUiLCJwcmV2RW5kRGF0ZSIsInByZXZSZXNwb25zZSIsInRpbWVTZXJpZXNSZXNwb25zZSIsIlByb21pc2UiLCJhbGwiLCJzdGF0dXMiLCJlcnJvclRleHQiLCJ0ZXh0IiwiRXJyb3IiLCJwcmV2RGF0YSIsInN1bW1hcnkiLCJncm91cGVkRGF0YUNvdW50IiwiZ3JvdXBlZF9kYXRhIiwibGVuZ3RoIiwidGltZVNlcmllc0NvdW50IiwiZm9ybWF0dGVkVGltZVNlcmllcyIsIm1hcCIsIml0ZW0iLCJkYXRlIiwicGVyaW9kIiwibmFtZSIsImNvc3QiLCJyZXF1ZXN0cyIsInRva2VucyIsImlucHV0X3Rva2VucyIsIm91dHB1dF90b2tlbnMiLCJsYXRlbmN5IiwiYXZnX2xhdGVuY3kiLCJtZXNzYWdlIiwiQXJyYXkiLCJfIiwiaSIsImgxIiwiYnV0dG9uIiwib25DbGljayIsInByZXZpb3VzU3VtbWFyeSIsImNvc3RUcmVuZCIsInRvdGFsX2Nvc3QiLCJyZXF1ZXN0VHJlbmQiLCJ0b3RhbF9yZXF1ZXN0cyIsImxhdGVuY3lUcmVuZCIsImF2ZXJhZ2VfbGF0ZW5jeSIsInN1Y2Nlc3NUcmVuZCIsInN1Y2Nlc3NfcmF0ZSIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwic2VsZWN0Iiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwib3B0aW9uIiwicm91bmQiLCJoMyIsInN2ZyIsInZpZXdCb3giLCJkZWZzIiwibGluZWFyR3JhZGllbnQiLCJ4MSIsInkxIiwieDIiLCJ5MiIsInN0b3AiLCJvZmZzZXQiLCJzdG9wQ29sb3IiLCJzdG9wT3BhY2l0eSIsImxpbmUiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsIm9wYWNpdHkiLCJwb2x5bGluZSIsImZpbGwiLCJwb2ludHMiLCJkIiwieCIsIm1heCIsIm1heENvc3QiLCJ5Iiwiam9pbiIsInBvbHlnb24iLCJtYXhMYXRlbmN5IiwidG90YWxfdG9rZW5zIiwibGVmdCIsInJhbmRvbSIsInRvcCIsInNsaWNlIiwicHJvdmlkZXIiLCJpbmRleCIsIndpZHRoIiwic3VjY2Vzc2Z1bF9yZXF1ZXN0cyIsInBhdGgiLCJBbmFseXRpY3NQYWdlIiwiZmFsbGJhY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});